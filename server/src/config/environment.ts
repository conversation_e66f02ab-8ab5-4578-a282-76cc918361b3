import dotenv from 'dotenv';
import path from 'path';

// Load environment variables
dotenv.config({ path: path.resolve(__dirname, '../../.env') });

interface Config {
  // Server
  nodeEnv: string;
  port: number;
  host: string;
  
  // Database
  mongodbUri: string;
  mongodbTestUri: string;
  
  // Redis
  redisUrl: string;
  redisPassword?: string;
  
  // JWT
  jwtSecret: string;
  jwtExpiresIn: string;
  jwtRefreshExpiresIn: string;
  
  // Email
  emailHost: string;
  emailPort: number;
  emailSecure: boolean;
  emailUser: string;
  emailPass: string;
  emailFrom: string;
  
  // Client
  clientUrl: string;
  allowedOrigins: string[];
  
  // File Upload
  uploadMaxSize: number;
  uploadAllowedTypes: string[];
  
  // Rate Limiting
  rateLimitWindowMs: number;
  rateLimitMaxRequests: number;
  
  // Security
  bcryptRounds: number;
  sessionSecret: string;
  
  // External APIs
  githubToken?: string;
  googleAnalyticsApiKey?: string;
  
  // Logging
  logLevel: string;
  logFile: string;
  
  // Admin
  adminEmail: string;
  adminPassword: string;
  adminName: string;
}

const requiredEnvVars = [
  'JWT_SECRET',
  'MONGODB_URI',
  'EMAIL_USER',
  'EMAIL_PASS',
  'SESSION_SECRET',
  'ADMIN_EMAIL',
  'ADMIN_PASSWORD',
];

// Validate required environment variables
const missingEnvVars = requiredEnvVars.filter(envVar => !process.env[envVar]);
if (missingEnvVars.length > 0) {
  throw new Error(`Missing required environment variables: ${missingEnvVars.join(', ')}`);
}

export const config: Config = {
  // Server
  nodeEnv: process.env.NODE_ENV || 'development',
  port: parseInt(process.env.PORT || '5000', 10),
  host: process.env.HOST || 'localhost',
  
  // Database
  mongodbUri: process.env.MONGODB_URI!,
  mongodbTestUri: process.env.MONGODB_TEST_URI || 'mongodb://localhost:27017/portfolio_test',
  
  // Redis
  redisUrl: process.env.REDIS_URL || 'redis://localhost:6379',
  redisPassword: process.env.REDIS_PASSWORD,
  
  // JWT
  jwtSecret: process.env.JWT_SECRET!,
  jwtExpiresIn: process.env.JWT_EXPIRES_IN || '7d',
  jwtRefreshExpiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '30d',
  
  // Email
  emailHost: process.env.EMAIL_HOST || 'smtp.gmail.com',
  emailPort: parseInt(process.env.EMAIL_PORT || '587', 10),
  emailSecure: process.env.EMAIL_SECURE === 'true',
  emailUser: process.env.EMAIL_USER!,
  emailPass: process.env.EMAIL_PASS!,
  emailFrom: process.env.EMAIL_FROM || '<EMAIL>',
  
  // Client
  clientUrl: process.env.CLIENT_URL || 'http://localhost:3000',
  allowedOrigins: process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:3000'],
  
  // File Upload
  uploadMaxSize: parseInt(process.env.UPLOAD_MAX_SIZE || '5242880', 10), // 5MB
  uploadAllowedTypes: process.env.UPLOAD_ALLOWED_TYPES?.split(',') || [
    'image/jpeg',
    'image/png',
    'image/webp',
    'application/pdf',
  ],
  
  // Rate Limiting
  rateLimitWindowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000', 10), // 15 minutes
  rateLimitMaxRequests: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100', 10),
  
  // Security
  bcryptRounds: parseInt(process.env.BCRYPT_ROUNDS || '12', 10),
  sessionSecret: process.env.SESSION_SECRET!,
  
  // External APIs
  githubToken: process.env.GITHUB_TOKEN,
  googleAnalyticsApiKey: process.env.GOOGLE_ANALYTICS_API_KEY,
  
  // Logging
  logLevel: process.env.LOG_LEVEL || 'info',
  logFile: process.env.LOG_FILE || 'logs/app.log',
  
  // Admin
  adminEmail: process.env.ADMIN_EMAIL!,
  adminPassword: process.env.ADMIN_PASSWORD!,
  adminName: process.env.ADMIN_NAME || 'Sahil Ali',
};

// Validate configuration
if (config.jwtSecret.length < 32) {
  throw new Error('JWT_SECRET must be at least 32 characters long');
}

if (config.bcryptRounds < 10 || config.bcryptRounds > 15) {
  throw new Error('BCRYPT_ROUNDS must be between 10 and 15');
}

if (config.uploadMaxSize > 10 * 1024 * 1024) { // 10MB
  throw new Error('UPLOAD_MAX_SIZE cannot exceed 10MB');
}

export default config;
