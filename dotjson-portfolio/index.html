<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="google-adsense-account" content="ca-pub-****************">
    <meta name="description" content="<PERSON><PERSON> is a Full Stack Developer and a Competitive Coder.">
    <meta name="keywords" content="<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>">
    <meta name="author" content="<PERSON><PERSON>">
    <meta name="description" content="thestarsahil">
    <meta name="keywords" content="thestarsahil">
    <meta name="author" content="thestarsahil">
    <meta name="author" content="Thestarsahil">
    <meta property="og:title" content="<PERSON><PERSON> Ali" />
    <meta property="og:description" content="Competitive Coder." />
    <meta property="og:url" content="https://thestarsahil.me" />
    <meta property="og:site_name" content="<PERSON><PERSON> - Portfolio" />
    <link rel="sitemap" type="application/xml" href="/sitemap.xml" />

    <title><PERSON><PERSON> </title>
    <link rel="icon" type="image/svg+xml" href="kali-logo.svg">
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="glitch.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/OwlCarousel2/2.3.4/assets/owl.carousel.min.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/OwlCarousel2/2.3.4/assets/owl.theme.default.min.css"/>
    <link href="https://fonts.googleapis.com/css2?family=Fira+Code:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/typed.js/2.0.12/typed.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/waypoints/4.0.1/jquery.waypoints.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/OwlCarousel2/2.3.4/owl.carousel.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
</head>

<body>
    <!-- Three.js canvas background -->
    <canvas id="bg"></canvas>

    <!-- Dark web effects -->
    <div class="scanline"></div>
    <div class="crt-flicker"></div>

    <div class="scroll-up-btn">
        <i class="fas fa-angle-up"></i>
    </div>
    <nav class="navbar">
        <div class="max-width">
            <div class="logo"><a href="#">Kali<span>@Sahil</span>:~$</a></div>
            <ul class="menu">
                <li><a href="#home" class="menu-btn">Home</a></li>
                <li><a href="#about" class="menu-btn">About</a></li>
                <li><a href="#services" class="menu-btn">Services</a></li>
                <li><a href="#skills" class="menu-btn">Skills</a></li>
                <li><a href="#teams" class="menu-btn">Projects</a></li>
                <li><a href="#contact" class="menu-btn">Contact</a></li>
            </ul>
            <div class="menu-btn">
                <i class="fas fa-bars"></i>
            </div>
        </div>
    </nav>



    <!-- home section start -->
    <section class="home" id="home">
        <div class="max-width">
            <div class="home-content terminal-window">
                <div class="terminal-header">
                    <div class="terminal-buttons">
                        <span class="terminal-button close"></span>
                        <span class="terminal-button minimize"></span>
                        <span class="terminal-button maximize"></span>
                    </div>
                    <div class="terminal-title glitch-text" data-text="kali@sahil: ~/portfolio">kali@sahil: ~/portfolio</div>
                </div>
                <div class="terminal-body">
                    <div class="terminal-line">$ whoami</div>
                    <div class="text-1">Hello, I am Sahil Ali</div>
                    <div class="text-2 glitch-text" data-text="Welcome to Dark Reality"> Welcome to Dark Reality</div>
                    <div class="terminal-line">$ profession</div>
                    <div class="text-3">I'm a <span class="typing"></span></div>
                    <div class="terminal-line">$ contact --hire</div>
                    <a href="#contact" class="terminal-btn">Hire me</a>
                </div>
            </div>
        </div>
    </section>

    <!-- about section start -->
    <section class="about" id="about">
        <div class="max-width">
            <h2 class="title">About me</h2>
            <div class="about-content">
                <div class="column left">
                    <img src="photo_2025-04-10_18-28-55.jpg" alt="">
                </div>
                <div class="column right">
                    <div class="text">I'm Sahil and I'm a <span class="typing-2"></span></div>
                    <p>Open Source Programmer</p>
                    <p>Ex-Microsoft MLSA</p>
                    <a href="Resume Sahil 2024.pdf">Download CV</a>
                </div>
            </div>
        </div>
    </section>

    <!-- services section start -->
    <section class="services" id="services">
        <div class="max-width">
            <h2 class="title">Hackathon and Open Source Contributor</h2>
            <div class="serv-content">
                <div class="card">
                    <div class="box">
                        <i class='fab fa-microsoft'></i>
                        <div class="text">Fastest Coder by Microsoft</div>
                        <p>Build a Finance App with JS and Github Copilot</p>
                    </div>
                </div>
                <div class="card">
                    <div class="box">
                        <i class='fas fa-robot'></i>
                        <div class="text">Contributor</div>
                        <p>Contributing to various projects related to AI, React, Cloud</p>
                    </div>
                </div>
                <div class="card">
                    <div class="box">
                        <i class="fas fa-code"></i>
                        <div class="text">Contributor
                            NLP Model by AI World
                        </div>
                        <p>Contributing to various projects related to AI, React, Cloud</p>
                    </div>
                </div>
            </div>
        </div>
        </div>
    </section>

    <!-- skills section start -->
    <section class="skills" id="skills">
        <div class="max-width">
            <h2 class="title"><img src="tor-logo.svg" alt="Tor Network" class="tor-logo-title"> <i class="fas fa-user-secret"></i> My skills</h2>
            <div class="skills-content">
                <div class="column left">
                    <div class="terminal-window skills-terminal">
                        <div class="terminal-header">
                            <div class="terminal-buttons">
                                <span class="terminal-button close"></span>
                                <span class="terminal-button minimize"></span>
                                <span class="terminal-button maximize"></span>
                            </div>
                            <div class="terminal-title glitch-text" data-text="kali@sahil: ~/skills">kali@sahil: ~/skills</div>
                        </div>
                        <div class="terminal-body">
                            <div class="terminal-line">$ cat skills.txt</div>
                            <div class="text glitch-text" data-text="Dark Web Skills & Expertise">Dark Web Skills & Expertise</div>
                            <p class="hacking-animation">
                               Computer Science Engineer with Trails operations
                            </p>
                            <div class="terminal-line">$ ./show_credentials.sh</div>
                            <a href="#" class="terminal-btn skills-btn">View Credentials</a>
                        </div>
                    </div>
                </div>
                <div class="column right">
                    <div class="bars">
                        <div class="info">
                            <span><i class="fas fa-terminal"></i> Prompt Engineering</span>
                            <span class="percentage-value">62%</span>
                        </div>
                        <div class="line html">
                            <div class="line-progress"></div>
                        </div>
                    </div>
                    <div class="bars">
                        <div class="info">
                            <span><i class="fas fa-robot"></i> AI Model</span>
                            <span class="percentage-value">31%</span>
                        </div>
                        <div class="line css">
                            <div class="line-progress"></div>
                        </div>
                    </div>
                    <div class="bars">
                        <div class="info">
                            <span><i class="fas fa-code"></i> Embedding C++</span>
                            <span class="percentage-value">65%</span>
                        </div>
                        <div class="line js">
                            <div class="line-progress"></div>
                        </div>
                    </div>
                    <div class="bars">
                        <div class="info">
                            <span><i class="fas fa-database"></i> Server and Database</span>
                            <span class="percentage-value">62%</span>
                        </div>
                        <div class="line php">
                            <div class="line-progress"></div>
                        </div>
                    </div>
                    <div class="bars">
                        <div class="info">
                            <span><i class="fas fa-sitemap"></i> Data Structure and Algorithm</span>
                            <span class="percentage-value">53%</span>
                        </div>
                        <div class="line mysql">
                            <div class="line-progress"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- teams section start -->
    <section class="teams" id="teams">
        <div class="max-width">
            <h2 class="title"><img src="tor-logo.svg" alt="Tor Network" class="tor-logo-title"> <i class="fas fa-project-diagram"></i> My Projects</h2>
            <div class="dark-web-banner">
                <div class="glitch-text" data-text="PROJECTS"> PROJECTS</div>
                <div class="encrypted-text">Access Level: Restricted</div>
            </div>
            <div class="carousel owl-carousel">
                <div class="card">
                    <div class="box">
                        <div class="project-image-container">
                            <img src="healtcare1.jpg" alt="Healthcare Project">
                            <div class="project-overlay">
                                <i class="fas fa-lock-open"></i>
                            </div>
                        </div>
                        <a href="https://github.com/thestarsahil/Mentalmate" class="project-link"><i class="fas fa-code-branch"></i> HealthCare</a>
                        <p>Building a 24x7 Healthcare Chatbot using NLP 24x7 support to patients</p>
                        <div class="project-tech">
                            <span class="tech-tag">NLP</span>
                            <span class="tech-tag">AI</span>
                            <span class="tech-tag">Python</span>
                        </div>
                    </div>
                </div>
                <div class="card">
                    <div class="box">
                        <div class="project-image-container">
                            <img src="weather.png" alt="Weather WebApp">
                            <div class="project-overlay">
                                <i class="fas fa-cloud"></i>
                            </div>
                        </div>
                        <div class="text">Weather WebApp</div>
                        <p>Web Application using HTML CSS JS with the help of Open Weather API</p>
                        <div class="project-tech">
                            <span class="tech-tag">HTML</span>
                            <span class="tech-tag">CSS</span>
                            <span class="tech-tag">JS</span>
                            <span class="tech-tag">API</span>
                        </div>
                    </div>
                </div>
                <div class="card">
                    <div class="box">
                        <div class="project-image-container">
                            <img src="Commercial-real-estate-in-india2.jpg" alt="Real Estate Project">
                            <div class="project-overlay">
                                <i class="fas fa-building"></i>
                            </div>
                        </div>
                        <div class="text">Contoso Real Estate</div>
                        <p>Microsoft Real Estate Project allows users to listed properties for sale or rent</p>
                        <div class="project-tech">
                            <span class="tech-tag">C#</span>
                            <span class="tech-tag">.NET</span>
                            <span class="tech-tag">Azure</span>
                        </div>
                    </div>
                </div>
                <div class="card">
                    <div class="box">
                        <div class="project-image-container">
                            <img src="OIP.jpeg" alt="CropForesight Project">
                            <div class="project-overlay">
                                <i class="fas fa-seedling"></i>
                            </div>
                        </div>
                        <div class="text">CropForesight</div>
                        <p>Assist farmers making smart choices about which crops to grow on their land</p>
                        <div class="project-tech">
                            <span class="tech-tag">ML</span>
                            <span class="tech-tag">Python</span>
                            <span class="tech-tag">Data</span>
                        </div>
                    </div>
                </div>
                <div class="card">
                    <div class="box">
                        <div class="project-image-container">
                            <img src="OIG.zRKLZGtrH78uPYps.jpeg" alt="Book Finder Project">
                            <div class="project-overlay">
                                <i class="fas fa-book"></i>
                            </div>
                        </div>
                        <div class="text">Book finder</div>
                        <p>Real-time project that helps users find and purchase books online FREE</p>
                        <div class="project-tech">
                            <span class="tech-tag">React</span>
                            <span class="tech-tag">Node</span>
                            <span class="tech-tag">MongoDB</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>



    <!-- contact section start -->
    <section class="contact" id="contact">
        <div class="max-width">
            <h2 class="title"><img src="tor-logo.svg" alt="Tor Network" class="tor-logo-title"> <i class="fas fa-terminal"></i> Contact me</h2>
            <div class="dark-web-banner contact-banner">
                <div class="glitch-text" data-text="SECURE COMMUNICATION CHANNEL">SECURE COMMUNICATION CHANNEL</div>
                <div class="encrypted-text">Encryption: PGP/GPG | Protocol: TOR</div>
            </div>
            <div class="contact-content">
                <div class="column left">
                    <div class="terminal-window contact-terminal">
                        <div class="terminal-header">
                            <div class="terminal-buttons">
                                <span class="terminal-button close"></span>
                                <span class="terminal-button minimize"></span>
                                <span class="terminal-button maximize"></span>
                            </div>
                            <div class="terminal-title glitch-text" data-text="kali@sahil: ~/contact">kali@sahil: ~/contact</div>
                        </div>
                        <div class="terminal-body">
                            <div class="terminal-line">$ cat contact_info.txt</div>
                            <div class="icons">
                                <div class="row">
                                    <i class="fas fa-user-secret"></i>
                                    <div class="info">
                                        <div class="head">Identity</div>
                                        <div class="sub-title glitch-text" data-text="Sahil Ali">Sahil Ali</div>
                                    </div>
                                </div>
                                <div class="row">
                                    <i class="fas fa-globe-americas"></i>
                                    <div class="info">
                                        <div class="head">Location</div>
                                        <div class="sub-title">Anonymous</div>
                                    </div>
                                </div>
                                <div class="row">
                                    <i class="fas fa-envelope"></i>
                                    <div class="info">
                                        <div class="head">Email</div>
                                        <div class="sub-title"><EMAIL></div>
                                    </div>
                                </div>
                                <div class="row">
                                    <i class="fas fa-key"></i>
                                    <div class="info">
                                        <div class="head">PGP Key</div>
                                        <div class="sub-title pgp-key">4B1D C371 57E3 2F8A...</div>
                                    </div>
                                </div>
                            </div>
                            <div class="terminal-line">$ ./verify_identity.sh</div>
                            <div class="verification-status">Identity Verified <i class="fas fa-check-circle"></i></div>
                        </div>
                    </div>
                </div>
                <div class="column right">
                    <div class="terminal-window message-terminal">
                        <div class="terminal-header">
                            <div class="terminal-buttons">
                                <span class="terminal-button close"></span>
                                <span class="terminal-button minimize"></span>
                                <span class="terminal-button maximize"></span>
                            </div>
                            <div class="terminal-title glitch-text" data-text="kali@sahil: ~/message">kali@sahil: ~/message</div>
                        </div>
                        <div class="terminal-body">
                            <div class="terminal-line">$ ./send_encrypted_message.sh</div>
                            <form action="#" class="encrypted-form">
                                <div class="fields">
                                    <div class="field name">
                                        <input type="text" placeholder="Identity" required>
                                        <span class="field-icon"><i class="fas fa-user-shield"></i></span>
                                    </div>
                                    <div class="field email">
                                        <input type="email" placeholder="Secure Email" required>
                                        <span class="field-icon"><i class="fas fa-at"></i></span>
                                    </div>
                                </div>
                                <div class="field">
                                    <input type="text" placeholder="Message Subject" required>
                                    <span class="field-icon"><i class="fas fa-heading"></i></span>
                                </div>
                                <div class="field textarea">
                                    <textarea cols="30" rows="10" placeholder="Encrypted Message..." required></textarea>
                                    <span class="field-icon textarea-icon"><i class="fas fa-lock"></i></span>
                                </div>
                                <div class="encryption-status">
                                    <span class="status-indicator">End-to-End Encryption Active</span>
                                    <span class="encryption-icon"><i class="fas fa-shield-alt"></i></span>
                                </div>
                                <div class="button-area">
                                    <button type="submit" class="terminal-btn">Transmit Secure Message</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- footer section start -->
    <footer>
        <div class="footer-landscape">
            <img src="photo_2022-02-08_20-11-12.jpg" alt="Dark Web Landscape" class="footer-landscape-img">
            <div class="footer-overlay"></div>
            <div class="footer-message">
                <div class="glitch-text" data-text="THANK YOU FOR VISITING">THANK YOU FOR VISITING</div>
                <div class="encrypted-text">Connection Terminated Safely</div>
                <div class="terminal-line">exit --secure</div>
            </div>
        </div>
        <div class="footer-content">
            <span>Created with Linux Community
                    Community</a> | <span class="far fa-copyright"></span> 2025 All rights reserved.</span>
            <div>
                <a href="https://github.com/thestarsahil">
                    <i class="fab fa-github social-icon"></i>
                </a>
                <a href="https://www.linkedin.com/in/thestarsahil/">
                    <i class="fab fa-linkedin social-icon"></i>
                </a>
                <a href="https://www.youtube.com/@thestarsahil">
                    <i class="fab fa-youtube social-icon"></i>
                </a>
            </div>
            <div class="onion-address">
                <img src="tor-logo.svg" alt="Tor Network" class="tor-logo-small">
                <span class="glitch-text" data-text="sahil3xvt7rjgd5woap4qd.onion">sahil3xvt7rjgd5woap4qd.onion</span>
            </div>
        </div>
    </footer>

    <script src="script.js"></script>
    <script src="three-background.js"></script>
</body>

</html>
