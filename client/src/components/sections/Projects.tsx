'use client';

import { motion } from 'framer-motion';
import Image from 'next/image';
import { ExternalLink, Github, Lock } from 'lucide-react';

const projects = [
  {
    title: 'HealthCare Chatbot',
    description: 'Building a 24x7 Healthcare Chatbot using NLP 24x7 support to patients',
    image: '/assets/healthcare.jpg',
    technologies: ['NLP', 'AI', 'Python'],
    githubUrl: 'https://github.com/thestarsahil/Mentalmate',
    liveUrl: null,
  },
  {
    title: 'Weather WebApp',
    description: 'Web Application using HTML CSS JS with the help of Open Weather API',
    image: '/assets/weather.png',
    technologies: ['HTML', 'CSS', 'JS', 'API'],
    githubUrl: null,
    liveUrl: null,
  },
  {
    title: 'Contoso Real Estate',
    description: 'Microsoft Real Estate Project allows users to listed properties for sale or rent',
    image: '/assets/real-estate.jpg',
    technologies: ['C#', '.NET', 'Azure'],
    githubUrl: null,
    liveUrl: null,
  },
  {
    title: 'CropForesight',
    description: 'Assist farmers making smart choices about which crops to grow on their land',
    image: '/assets/crop.jpg',
    technologies: ['ML', 'Python', 'Data'],
    githubUrl: null,
    liveUrl: null,
  },
  {
    title: 'Book Finder',
    description: 'Real-time project that helps users find and purchase books online FREE',
    image: '/assets/book-finder.jpg',
    technologies: ['React', 'Node', 'MongoDB'],
    githubUrl: null,
    liveUrl: null,
  },
];

export function Projects() {
  return (
    <section id="projects" className="section-padding">
      <div className="container-custom">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl md:text-4xl font-bold text-darkweb-text mb-4 flex items-center justify-center gap-4">
            <img src="/assets/tor-logo.svg" alt="Tor Network" className="w-8 h-8" />
            <span className="text-darkweb-accent glitch-text" data-text="My Projects">
              My Projects
            </span>
          </h2>
          <div className="w-24 h-1 bg-darkweb-accent mx-auto mb-8"></div>
          
          <div className="bg-darkweb-card border border-darkweb-border rounded-lg p-6 max-w-md mx-auto">
            <div className="glitch-text text-darkweb-accent font-bold mb-2" data-text="PROJECTS">
              PROJECTS
            </div>
            <div className="text-darkweb-muted text-sm">Access Level: Restricted</div>
          </div>
        </motion.div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {projects.map((project, index) => (
            <motion.div
              key={project.title}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="project-card"
            >
              <div className="relative overflow-hidden rounded-t-lg">
                <Image
                  src={project.image}
                  alt={project.title}
                  width={400}
                  height={250}
                  className="project-image"
                />
                <div className="project-overlay">
                  <Lock className="w-8 h-8 text-darkweb-accent" />
                </div>
              </div>
              
              <div className="p-6 space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-xl font-bold text-darkweb-text group-hover:text-darkweb-accent transition-colors">
                    {project.title}
                  </h3>
                  <div className="flex gap-2">
                    {project.githubUrl && (
                      <a
                        href={project.githubUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="p-2 text-darkweb-muted hover:text-darkweb-accent transition-colors"
                      >
                        <Github className="w-4 h-4" />
                      </a>
                    )}
                    {project.liveUrl && (
                      <a
                        href={project.liveUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="p-2 text-darkweb-muted hover:text-darkweb-accent transition-colors"
                      >
                        <ExternalLink className="w-4 h-4" />
                      </a>
                    )}
                  </div>
                </div>
                
                <p className="text-darkweb-muted text-sm leading-relaxed">
                  {project.description}
                </p>
                
                <div className="flex flex-wrap gap-2">
                  {project.technologies.map((tech) => (
                    <span key={tech} className="tech-tag">
                      {tech}
                    </span>
                  ))}
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
}
