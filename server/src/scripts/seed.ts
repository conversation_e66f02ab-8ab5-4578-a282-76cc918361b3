import { connectDatabase, disconnectDatabase } from '@/config/database';
import { User } from '@/models/User';
import { Project } from '@/models/Project';
import { config } from '@/config/environment';
import { logger } from '@/utils/logger';
import { UserRole, ProjectCategory, ProjectStatus } from '@portfolio/shared';

const seedUsers = async () => {
  logger.info('Seeding users...');
  
  // Create admin user
  const adminExists = await User.findOne({ email: config.adminEmail });
  if (!adminExists) {
    await User.create({
      email: config.adminEmail,
      name: config.adminName,
      password: config.adminPassword,
      role: UserRole.ADMIN,
    });
    logger.info('Admin user created');
  } else {
    logger.info('Admin user already exists');
  }
};

const seedProjects = async () => {
  logger.info('Seeding projects...');
  
  const projectsData = [
    {
      title: 'HealthCare Chatbot',
      description: 'Building a 24x7 Healthcare Chatbot using NLP 24x7 support to patients',
      longDescription: 'A comprehensive healthcare chatbot built using Natural Language Processing to provide 24/7 support to patients. The system can understand medical queries, provide basic health information, and guide users to appropriate medical resources.',
      image: '/assets/healthcare.jpg',
      images: ['/assets/healthcare1.jpg', '/assets/healthcare2.jpg'],
      technologies: ['NLP', 'AI', 'Python', 'TensorFlow', 'Flask'],
      githubUrl: 'https://github.com/thestarsahil/Mentalmate',
      featured: true,
      category: ProjectCategory.AI_ML,
      status: ProjectStatus.COMPLETED,
      startDate: new Date('2023-06-01'),
      endDate: new Date('2023-09-01'),
    },
    {
      title: 'Weather WebApp',
      description: 'Web Application using HTML CSS JS with the help of Open Weather API',
      longDescription: 'A responsive weather application that provides real-time weather information using the OpenWeather API. Features include current weather, 5-day forecast, and location-based weather data.',
      image: '/assets/weather.png',
      technologies: ['HTML', 'CSS', 'JavaScript', 'API', 'Responsive Design'],
      featured: false,
      category: ProjectCategory.WEB_DEVELOPMENT,
      status: ProjectStatus.COMPLETED,
      startDate: new Date('2023-03-01'),
      endDate: new Date('2023-04-01'),
    },
    {
      title: 'Contoso Real Estate',
      description: 'Microsoft Real Estate Project allows users to listed properties for sale or rent',
      longDescription: 'A comprehensive real estate platform built for Microsoft that allows users to list, search, and manage properties for sale or rent. Features include property management, user authentication, and advanced search capabilities.',
      image: '/assets/real-estate.jpg',
      technologies: ['C#', '.NET', 'Azure', 'SQL Server', 'Entity Framework'],
      featured: true,
      category: ProjectCategory.WEB_DEVELOPMENT,
      status: ProjectStatus.COMPLETED,
      startDate: new Date('2023-09-01'),
      endDate: new Date('2023-12-01'),
    },
    {
      title: 'CropForesight',
      description: 'Assist farmers making smart choices about which crops to grow on their land',
      longDescription: 'An AI-powered agricultural decision support system that helps farmers make informed decisions about crop selection based on soil conditions, weather patterns, and market trends.',
      image: '/assets/crop.jpg',
      technologies: ['Machine Learning', 'Python', 'Data Science', 'Pandas', 'Scikit-learn'],
      featured: false,
      category: ProjectCategory.AI_ML,
      status: ProjectStatus.COMPLETED,
      startDate: new Date('2023-01-01'),
      endDate: new Date('2023-05-01'),
    },
    {
      title: 'Book Finder',
      description: 'Real-time project that helps users find and purchase books online FREE',
      longDescription: 'A full-stack web application that helps users discover and find books online. Features include book search, recommendations, user reviews, and integration with multiple book APIs.',
      image: '/assets/book-finder.jpg',
      technologies: ['React', 'Node.js', 'MongoDB', 'Express', 'REST API'],
      featured: true,
      category: ProjectCategory.WEB_DEVELOPMENT,
      status: ProjectStatus.COMPLETED,
      startDate: new Date('2022-10-01'),
      endDate: new Date('2023-01-01'),
    },
    {
      title: 'Portfolio Website v2',
      description: 'Modern full-stack portfolio with client-server architecture',
      longDescription: 'A complete redesign of my portfolio website using modern technologies and proper client-server architecture. Features include dark web theme, real-time analytics, contact form, and admin dashboard.',
      image: '/assets/portfolio-v2.jpg',
      technologies: ['Next.js', 'TypeScript', 'Node.js', 'MongoDB', 'Three.js', 'Tailwind CSS'],
      githubUrl: 'https://github.com/thestarsahil/Portfolio',
      liveUrl: 'https://thestarsahil.me',
      featured: true,
      category: ProjectCategory.WEB_DEVELOPMENT,
      status: ProjectStatus.IN_PROGRESS,
      startDate: new Date('2024-01-01'),
    },
  ];

  for (const projectData of projectsData) {
    const existingProject = await Project.findOne({ title: projectData.title });
    if (!existingProject) {
      await Project.create(projectData);
      logger.info(`Project "${projectData.title}" created`);
    } else {
      logger.info(`Project "${projectData.title}" already exists`);
    }
  }
};

const seedDatabase = async () => {
  try {
    logger.info('Starting database seeding...');
    
    await connectDatabase();
    
    await seedUsers();
    await seedProjects();
    
    logger.info('Database seeding completed successfully');
  } catch (error) {
    logger.error('Database seeding failed:', error);
    process.exit(1);
  } finally {
    await disconnectDatabase();
  }
};

// Run seeding if this file is executed directly
if (require.main === module) {
  seedDatabase();
}

export { seedDatabase };
