'use client';

import { motion } from 'framer-motion';
import { Terminal, Bot, Code, Database, Cpu } from 'lucide-react';

const skills = [
  { name: 'Prompt Engineering', level: 62, icon: Terminal },
  { name: 'AI Model', level: 31, icon: Bo<PERSON> },
  { name: 'Embedding C++', level: 65, icon: Code },
  { name: 'Server and Database', level: 62, icon: Database },
  { name: 'Data Structure and Algorithm', level: 53, icon: Cpu },
];

export function Skills() {
  return (
    <section id="skills" className="section-padding bg-darkweb-surface/30">
      <div className="container-custom">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl md:text-4xl font-bold text-darkweb-text mb-4 flex items-center justify-center gap-4">
            <img src="/assets/tor-logo.svg" alt="Tor Network" className="w-8 h-8" />
            <span className="text-darkweb-accent glitch-text" data-text="My skills">
              My skills
            </span>
          </h2>
          <div className="w-24 h-1 bg-darkweb-accent mx-auto"></div>
        </motion.div>

        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Terminal Window */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="terminal-window"
          >
            <div className="terminal-header">
              <div className="terminal-buttons">
                <span className="terminal-button close"></span>
                <span className="terminal-button minimize"></span>
                <span className="terminal-button maximize"></span>
              </div>
              <div className="terminal-title glitch-text" data-text="kali@sahil: ~/skills">
                kali@sahil: ~/skills
              </div>
            </div>
            <div className="terminal-body">
              <div className="terminal-line">cat skills.txt</div>
              <div className="text-darkweb-accent font-bold mb-4 glitch-text" data-text="Dark Web Skills & Expertise">
                Dark Web Skills & Expertise
              </div>
              <p className="text-darkweb-text mb-4 typing-animation">
                Computer Science Engineer with Trails operations
              </p>
              <div className="terminal-line">./show_credentials.sh</div>
              <button className="btn-terminal mt-4">
                View Credentials
              </button>
            </div>
          </motion.div>

          {/* Skills Bars */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="space-y-6"
          >
            {skills.map((skill, index) => (
              <motion.div
                key={skill.name}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="space-y-2"
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <skill.icon className="w-4 h-4 text-darkweb-accent" />
                    <span className="text-darkweb-text font-mono text-sm">
                      {skill.name}
                    </span>
                  </div>
                  <span className="text-darkweb-accent font-mono text-sm">
                    {skill.level}%
                  </span>
                </div>
                
                <div className="skill-bar">
                  <motion.div
                    className="skill-progress"
                    initial={{ width: 0 }}
                    whileInView={{ width: `${skill.level}%` }}
                    transition={{ duration: 1.5, delay: index * 0.1 }}
                    viewport={{ once: true }}
                  />
                </div>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </div>
    </section>
  );
}
