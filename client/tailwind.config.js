/** @type {import('tailwindcss').Config} */
module.exports = {
  darkMode: ['class'],
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    container: {
      center: true,
      padding: '2rem',
      screens: {
        '2xl': '1400px',
      },
    },
    extend: {
      colors: {
        // Dark Web Theme Colors
        darkweb: {
          bg: '#0a0a0a',
          surface: '#121212',
          card: '#1a1a1a',
          border: '#333333',
          text: '#ffffff',
          muted: '#888888',
          accent: '#00ffff',
          purple: '#6e0b75',
          red: '#8b0000',
          green: '#00ff00',
          yellow: '#ffff00',
        },
        // Kali Linux Colors
        kali: {
          blue: '#0066cc',
          'dark-blue': '#004080',
          black: '#121212',
          gray: '#333333',
          'light-gray': '#555555',
          'terminal-green': '#00ff00',
          'terminal-red': '#ff0000',
          'terminal-yellow': '#ffff00',
        },
        border: 'hsl(var(--border))',
        input: 'hsl(var(--input))',
        ring: 'hsl(var(--ring))',
        background: 'hsl(var(--background))',
        foreground: 'hsl(var(--foreground))',
        primary: {
          DEFAULT: 'hsl(var(--primary))',
          foreground: 'hsl(var(--primary-foreground))',
        },
        secondary: {
          DEFAULT: 'hsl(var(--secondary))',
          foreground: 'hsl(var(--secondary-foreground))',
        },
        destructive: {
          DEFAULT: 'hsl(var(--destructive))',
          foreground: 'hsl(var(--destructive-foreground))',
        },
        muted: {
          DEFAULT: 'hsl(var(--muted))',
          foreground: 'hsl(var(--muted-foreground))',
        },
        accent: {
          DEFAULT: 'hsl(var(--accent))',
          foreground: 'hsl(var(--accent-foreground))',
        },
        popover: {
          DEFAULT: 'hsl(var(--popover))',
          foreground: 'hsl(var(--popover-foreground))',
        },
        card: {
          DEFAULT: 'hsl(var(--card))',
          foreground: 'hsl(var(--card-foreground))',
        },
      },
      borderRadius: {
        lg: 'var(--radius)',
        md: 'calc(var(--radius) - 2px)',
        sm: 'calc(var(--radius) - 4px)',
      },
      fontFamily: {
        mono: ['Fira Code', 'Monaco', 'Cascadia Code', 'Roboto Mono', 'monospace'],
        sans: ['Inter', 'system-ui', 'sans-serif'],
        display: ['Orbitron', 'system-ui', 'sans-serif'],
      },
      animation: {
        'glitch': 'glitch 1s infinite linear alternate-reverse',
        'glitch-skew': 'glitch-skew 1s infinite linear alternate-reverse',
        'typing': 'typing 3.5s steps(40, end)',
        'blink': 'blink 1s infinite',
        'scanline': 'scanline 2s linear infinite',
        'flicker': 'flicker 0.3s infinite',
        'matrix-rain': 'matrix-rain 20s linear infinite',
        'pulse-glow': 'pulse-glow 2s ease-in-out infinite alternate',
        'float': 'float 6s ease-in-out infinite',
        'slide-up': 'slide-up 0.5s ease-out',
        'slide-down': 'slide-down 0.5s ease-out',
        'fade-in': 'fade-in 0.5s ease-out',
        'scale-in': 'scale-in 0.3s ease-out',
      },
      keyframes: {
        glitch: {
          '0%': { clip: 'rect(31px, 9999px, 94px, 0)', transform: 'skew(0.85deg)' },
          '5%': { clip: 'rect(70px, 9999px, 71px, 0)', transform: 'skew(0.17deg)' },
          '10%': { clip: 'rect(75px, 9999px, 92px, 0)', transform: 'skew(0.4deg)' },
          '15%': { clip: 'rect(12px, 9999px, 23px, 0)', transform: 'skew(0.01deg)' },
          '20%': { clip: 'rect(18px, 9999px, 13px, 0)', transform: 'skew(0.65deg)' },
          '25%': { clip: 'rect(38px, 9999px, 43px, 0)', transform: 'skew(0.29deg)' },
          '30%': { clip: 'rect(81px, 9999px, 4px, 0)', transform: 'skew(0.02deg)' },
          '35%': { clip: 'rect(86px, 9999px, 25px, 0)', transform: 'skew(0.08deg)' },
          '40%': { clip: 'rect(93px, 9999px, 46px, 0)', transform: 'skew(0.71deg)' },
          '45%': { clip: 'rect(100px, 9999px, 85px, 0)', transform: 'skew(0.65deg)' },
          '50%': { clip: 'rect(25px, 9999px, 100px, 0)', transform: 'skew(0.89deg)' },
          '100%': { clip: 'rect(98px, 9999px, 71px, 0)', transform: 'skew(0.01deg)' },
        },
        'glitch-skew': {
          '0%': { transform: 'skew(-1deg)' },
          '10%': { transform: 'skew(0.5deg)' },
          '20%': { transform: 'skew(0.9deg)' },
          '30%': { transform: 'skew(0.3deg)' },
          '40%': { transform: 'skew(-0.5deg)' },
          '50%': { transform: 'skew(-1deg)' },
          '60%': { transform: 'skew(0.7deg)' },
          '70%': { transform: 'skew(0.2deg)' },
          '80%': { transform: 'skew(-0.2deg)' },
          '90%': { transform: 'skew(0.5deg)' },
          '100%': { transform: 'skew(-0.1deg)' },
        },
        typing: {
          'from': { width: '0' },
          'to': { width: '100%' },
        },
        blink: {
          '0%, 50%': { opacity: '1' },
          '51%, 100%': { opacity: '0' },
        },
        scanline: {
          '0%': { transform: 'translateY(-100vh)' },
          '100%': { transform: 'translateY(100vh)' },
        },
        flicker: {
          '0%': { opacity: '0.1' },
          '2%': { opacity: '0.1' },
          '4%': { opacity: '0.05' },
          '6%': { opacity: '0.1' },
          '8%': { opacity: '0.05' },
          '10%': { opacity: '0.02' },
          '20%': { opacity: '0.05' },
          '30%': { opacity: '0.02' },
          '40%': { opacity: '0.06' },
          '50%': { opacity: '0.05' },
          '60%': { opacity: '0.02' },
          '70%': { opacity: '0.09' },
          '80%': { opacity: '0.03' },
          '90%': { opacity: '0.06' },
          '100%': { opacity: '0.1' },
        },
        'matrix-rain': {
          '0%': { transform: 'translateY(-100%)' },
          '100%': { transform: 'translateY(100vh)' },
        },
        'pulse-glow': {
          '0%': { boxShadow: '0 0 5px rgba(0, 255, 255, 0.5)' },
          '100%': { boxShadow: '0 0 20px rgba(0, 255, 255, 0.8), 0 0 30px rgba(0, 255, 255, 0.6)' },
        },
        float: {
          '0%, 100%': { transform: 'translateY(0px)' },
          '50%': { transform: 'translateY(-20px)' },
        },
        'slide-up': {
          '0%': { transform: 'translateY(100%)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        'slide-down': {
          '0%': { transform: 'translateY(-100%)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        'fade-in': {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        'scale-in': {
          '0%': { transform: 'scale(0.9)', opacity: '0' },
          '100%': { transform: 'scale(1)', opacity: '1' },
        },
      },
      backgroundImage: {
        'gradient-radial': 'radial-gradient(var(--tw-gradient-stops))',
        'gradient-conic': 'conic-gradient(from 180deg at 50% 50%, var(--tw-gradient-stops))',
        'matrix-pattern': 'url("data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%2300ff00" fill-opacity="0.1"%3E%3Cpath d="M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")',
      },
      backdropBlur: {
        xs: '2px',
      },
      screens: {
        xs: '475px',
      },
    },
  },
  plugins: [],
};
