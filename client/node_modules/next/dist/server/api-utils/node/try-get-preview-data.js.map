{"version": 3, "sources": ["../../../../src/server/api-utils/node/try-get-preview-data.ts"], "names": ["tryGetPreviewData", "req", "res", "options", "multiZoneDraftMode", "cookies", "checkIsOnDemandRevalidate", "isOnDemandRevalidate", "SYMBOL_PREVIEW_DATA", "headers", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "from", "RequestCookies", "previewModeId", "get", "COOKIE_NAME_PRERENDER_BYPASS", "value", "tokenPreviewData", "COOKIE_NAME_PRERENDER_DATA", "data", "Object", "defineProperty", "enumerable", "clearPreviewData", "encryptedPreviewData", "jsonwebtoken", "require", "verify", "previewModeSigningKey", "decryptWithSecret", "decryptedPreviewData", "<PERSON><PERSON><PERSON>", "previewModeEncryptionKey", "JSON", "parse"], "mappings": ";;;;+BAgBgBA;;;eAAAA;;;kBAd0B;uBAUnC;yBACwB;yBACA;AAExB,SAASA,kBACdC,GAAgD,EAChDC,GAAsC,EACtCC,OAA0B,EAC1BC,kBAA2B;QAiBLC,cACGA;IAhBzB,0DAA0D;IAC1D,cAAc;IACd,IAAIF,WAAWG,IAAAA,2BAAyB,EAACL,KAAKE,SAASI,oBAAoB,EAAE;QAC3E,OAAO;IACT;IAEA,sCAAsC;IACtC,iDAAiD;IACjD,IAAIC,0BAAmB,IAAIP,KAAK;QAC9B,OAAO,AAACA,GAAW,CAACO,0BAAmB,CAAC;IAC1C;IAEA,MAAMC,UAAUC,uBAAc,CAACC,IAAI,CAACV,IAAIQ,OAAO;IAC/C,MAAMJ,UAAU,IAAIO,uBAAc,CAACH;IAEnC,MAAMI,iBAAgBR,eAAAA,QAAQS,GAAG,CAACC,mCAA4B,sBAAxCV,aAA2CW,KAAK;IACtE,MAAMC,oBAAmBZ,gBAAAA,QAAQS,GAAG,CAACI,iCAA0B,sBAAtCb,cAAyCW,KAAK;IAEvE,2DAA2D;IAC3D,IACEH,iBACA,CAACI,oBACDJ,kBAAkBV,QAAQU,aAAa,EACvC;QACA,yCAAyC;QACzC,4CAA4C;QAC5C,4CAA4C;QAC5C,MAAMM,OAAO,CAAC;QACdC,OAAOC,cAAc,CAACpB,KAAKO,0BAAmB,EAAE;YAC9CQ,OAAOG;YACPG,YAAY;QACd;QACA,OAAOH;IACT;IAEA,+BAA+B;IAC/B,IAAI,CAACN,iBAAiB,CAACI,kBAAkB;QACvC,OAAO;IACT;IAEA,8CAA8C;IAC9C,IAAI,CAACJ,iBAAiB,CAACI,kBAAkB;QACvC,IAAI,CAACb,oBAAoB;YACvBmB,IAAAA,uBAAgB,EAACrB;QACnB;QACA,OAAO;IACT;IAEA,6CAA6C;IAC7C,IAAIW,kBAAkBV,QAAQU,aAAa,EAAE;QAC3C,IAAI,CAACT,oBAAoB;YACvBmB,IAAAA,uBAAgB,EAACrB;QACnB;QACA,OAAO;IACT;IAEA,IAAIsB;IAGJ,IAAI;QACF,MAAMC,eACJC,QAAQ;QACVF,uBAAuBC,aAAaE,MAAM,CACxCV,kBACAd,QAAQyB,qBAAqB;IAEjC,EAAE,OAAM;QACN,aAAa;QACbL,IAAAA,uBAAgB,EAACrB;QACjB,OAAO;IACT;IAEA,MAAM,EAAE2B,iBAAiB,EAAE,GACzBH,QAAQ;IACV,MAAMI,uBAAuBD,kBAC3BE,OAAOpB,IAAI,CAACR,QAAQ6B,wBAAwB,GAC5CR,qBAAqBL,IAAI;IAG3B,IAAI;QACF,qCAAqC;QACrC,MAAMA,OAAOc,KAAKC,KAAK,CAACJ;QACxB,eAAe;QACfV,OAAOC,cAAc,CAACpB,KAAKO,0BAAmB,EAAE;YAC9CQ,OAAOG;YACPG,YAAY;QACd;QACA,OAAOH;IACT,EAAE,OAAM;QACN,OAAO;IACT;AACF"}