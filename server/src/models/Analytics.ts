import mongoose, { Document, Schema } from 'mongoose';
import { AnalyticsEvent } from '@portfolio/shared';

export interface IAnalytics extends Document {
  event: AnalyticsEvent;
  page: string;
  userAgent?: string;
  ipAddress?: string;
  country?: string;
  city?: string;
  referrer?: string;
  sessionId?: string;
  userId?: string;
  metadata?: Record<string, any>;
  timestamp: Date;
}

const analyticsSchema = new Schema<IAnalytics>(
  {
    event: {
      type: String,
      enum: Object.values(AnalyticsEvent),
      required: [true, 'Event type is required'],
    },
    page: {
      type: String,
      required: [true, 'Page is required'],
      trim: true,
    },
    userAgent: {
      type: String,
      default: null,
    },
    ipAddress: {
      type: String,
      default: null,
    },
    country: {
      type: String,
      default: null,
    },
    city: {
      type: String,
      default: null,
    },
    referrer: {
      type: String,
      default: null,
    },
    sessionId: {
      type: String,
      default: null,
    },
    userId: {
      type: String,
      default: null,
    },
    metadata: {
      type: Schema.Types.Mixed,
      default: null,
    },
    timestamp: {
      type: Date,
      default: Date.now,
    },
  },
  {
    timestamps: false, // We use custom timestamp field
    toJSON: {
      transform: function (doc, ret) {
        delete ret.__v;
        return ret;
      },
    },
  }
);

// Indexes for performance
analyticsSchema.index({ event: 1 });
analyticsSchema.index({ page: 1 });
analyticsSchema.index({ timestamp: -1 });
analyticsSchema.index({ sessionId: 1 });
analyticsSchema.index({ userId: 1 });
analyticsSchema.index({ ipAddress: 1 });

// Compound indexes
analyticsSchema.index({ event: 1, timestamp: -1 });
analyticsSchema.index({ page: 1, timestamp: -1 });

// TTL index to automatically delete old analytics data (keep for 2 years)
analyticsSchema.index({ timestamp: 1 }, { expireAfterSeconds: 63072000 }); // 2 years

// Static methods
analyticsSchema.statics.findByEvent = function (event: AnalyticsEvent, limit: number = 100) {
  return this.find({ event }).sort({ timestamp: -1 }).limit(limit);
};

analyticsSchema.statics.findByPage = function (page: string, limit: number = 100) {
  return this.find({ page }).sort({ timestamp: -1 }).limit(limit);
};

analyticsSchema.statics.findByDateRange = function (startDate: Date, endDate: Date) {
  return this.find({
    timestamp: {
      $gte: startDate,
      $lte: endDate,
    },
  }).sort({ timestamp: -1 });
};

analyticsSchema.statics.getEventCounts = function (startDate?: Date, endDate?: Date) {
  const matchStage: any = {};
  
  if (startDate && endDate) {
    matchStage.timestamp = {
      $gte: startDate,
      $lte: endDate,
    };
  }

  return this.aggregate([
    { $match: matchStage },
    {
      $group: {
        _id: '$event',
        count: { $sum: 1 },
      },
    },
    { $sort: { count: -1 } },
  ]);
};

analyticsSchema.statics.getPageViews = function (startDate?: Date, endDate?: Date) {
  const matchStage: any = { event: AnalyticsEvent.PAGE_VIEW };
  
  if (startDate && endDate) {
    matchStage.timestamp = {
      $gte: startDate,
      $lte: endDate,
    };
  }

  return this.aggregate([
    { $match: matchStage },
    {
      $group: {
        _id: '$page',
        views: { $sum: 1 },
        uniqueVisitors: { $addToSet: '$ipAddress' },
      },
    },
    {
      $project: {
        page: '$_id',
        views: 1,
        uniqueVisitors: { $size: '$uniqueVisitors' },
      },
    },
    { $sort: { views: -1 } },
  ]);
};

analyticsSchema.statics.getDailyStats = function (days: number = 30) {
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - days);

  return this.aggregate([
    {
      $match: {
        timestamp: { $gte: startDate },
      },
    },
    {
      $group: {
        _id: {
          year: { $year: '$timestamp' },
          month: { $month: '$timestamp' },
          day: { $dayOfMonth: '$timestamp' },
        },
        totalEvents: { $sum: 1 },
        uniqueVisitors: { $addToSet: '$ipAddress' },
        pageViews: {
          $sum: {
            $cond: [{ $eq: ['$event', AnalyticsEvent.PAGE_VIEW] }, 1, 0],
          },
        },
      },
    },
    {
      $project: {
        date: {
          $dateFromParts: {
            year: '$_id.year',
            month: '$_id.month',
            day: '$_id.day',
          },
        },
        totalEvents: 1,
        uniqueVisitors: { $size: '$uniqueVisitors' },
        pageViews: 1,
      },
    },
    { $sort: { date: 1 } },
  ]);
};

export const Analytics = mongoose.model<IAnalytics>('Analytics', analyticsSchema);
