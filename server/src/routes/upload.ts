import { Router } from 'express';
import { authenticate, authorize } from '@/middleware/auth';
import { HTTP_STATUS, UserRole } from '@portfolio/shared';

const router = Router();

// Placeholder upload routes (admin only)
router.use(authenticate);
router.use(authorize(UserRole.ADMIN));

router.post('/image', (req, res) => {
  res.status(HTTP_STATUS.OK).json({
    success: true,
    message: 'Image upload endpoint coming soon',
    timestamp: new Date().toISOString(),
  });
});

router.post('/file', (req, res) => {
  res.status(HTTP_STATUS.OK).json({
    success: true,
    message: 'File upload endpoint coming soon',
    timestamp: new Date().toISOString(),
  });
});

export default router;
