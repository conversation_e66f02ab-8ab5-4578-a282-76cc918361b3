import { Router } from 'express';
import { body } from 'express-validator';
import { Analytics } from '@/models/Analytics';
import { asyncHand<PERSON> } from '@/middleware/errorHandler';
import { authenticate, authorize, optionalAuth } from '@/middleware/auth';
import { HTTP_STATUS, AnalyticsEvent, UserRole } from '@portfolio/shared';

const router = Router();

// Track analytics event (public endpoint)
router.post('/track', [
  body('event').isIn(Object.values(AnalyticsEvent)).withMessage('Invalid event type'),
  body('page').isString().isLength({ min: 1 }).withMessage('Page is required'),
], optionalAuth, asyncHandler(async (req, res) => {
  const { event, page, metadata } = req.body;
  
  // Get client information
  const ipAddress = req.ip;
  const userAgent = req.get('User-Agent');
  const referrer = req.get('Referer');
  
  // Create analytics record
  await Analytics.create({
    event,
    page,
    userAgent,
    ipAddress,
    referrer,
    userId: req.user?.id,
    metadata,
  });

  res.status(HTTP_STATUS.CREATED).json({
    success: true,
    message: 'Event tracked successfully',
    timestamp: new Date().toISOString(),
  });
}));

// Get analytics stats (admin only)
router.get('/stats', authenticate, authorize(UserRole.ADMIN), asyncHandler(async (req, res) => {
  const { days = 30 } = req.query;
  
  const [
    totalEvents,
    dailyStats,
    eventCounts,
    pageViews,
  ] = await Promise.all([
    Analytics.countDocuments(),
    Analytics.getDailyStats(parseInt(days as string)),
    Analytics.getEventCounts(),
    Analytics.getPageViews(),
  ]);

  res.status(HTTP_STATUS.OK).json({
    success: true,
    data: {
      totalEvents,
      dailyStats,
      eventCounts,
      pageViews,
    },
    timestamp: new Date().toISOString(),
  });
}));

// Get page views (admin only)
router.get('/pageviews', authenticate, authorize(UserRole.ADMIN), asyncHandler(async (req, res) => {
  const { startDate, endDate } = req.query;
  
  let start, end;
  if (startDate && endDate) {
    start = new Date(startDate as string);
    end = new Date(endDate as string);
  }

  const pageViews = await Analytics.getPageViews(start, end);

  res.status(HTTP_STATUS.OK).json({
    success: true,
    data: pageViews,
    timestamp: new Date().toISOString(),
  });
}));

export default router;
