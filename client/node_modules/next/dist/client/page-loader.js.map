{"version": 3, "sources": ["../../src/client/page-loader.ts"], "names": ["<PERSON><PERSON><PERSON><PERSON>", "getPageList", "process", "env", "NODE_ENV", "getClientBuildManifest", "then", "manifest", "sortedPages", "window", "__DEV_PAGES_MANIFEST", "pages", "promisedDevPagesManifest", "fetch", "assetPrefix", "DEV_CLIENT_PAGES_MANIFEST", "credentials", "res", "json", "catch", "err", "console", "log", "Error", "getMiddleware", "middlewareMatchers", "__NEXT_MIDDLEWARE_MATCHERS", "__MIDDLEWARE_MATCHERS", "undefined", "__DEV_MIDDLEWARE_MATCHERS", "promisedMiddlewareMatchers", "buildId", "DEV_MIDDLEWARE_MANIFEST", "matchers", "getDataHref", "params", "<PERSON><PERSON><PERSON>", "href", "locale", "pathname", "hrefPathname", "query", "search", "parseRelativeUrl", "asPathname", "route", "removeTrailingSlash", "getHrefForSlug", "path", "dataRoute", "getAssetPathFromRoute", "addLocale", "addBasePath", "skipInterpolation", "isDynamicRoute", "interpolateAs", "result", "_isSsg", "promisedSsgManifest", "has", "loadPage", "routeLoader", "loadRoute", "page", "component", "mod", "exports", "styleSheets", "styles", "map", "o", "text", "content", "error", "prefetch", "constructor", "createRouteLoader", "Promise", "resolve", "__SSG_MANIFEST", "__SSG_MANIFEST_CB"], "mappings": ";;;;;;;eAgCqBA;;;;6BA7BO;+BACE;gFACI;2BACR;2BACK;kCACE;qCACG;6BACsB;2BAInD;AAkBQ,MAAMA;IA0BnBC,cAAc;QACZ,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;YACzC,OAAOC,IAAAA,mCAAsB,IAAGC,IAAI,CAAC,CAACC,WAAaA,SAASC,WAAW;QACzE,OAAO;YACL,IAAIC,OAAOC,oBAAoB,EAAE;gBAC/B,OAAOD,OAAOC,oBAAoB,CAACC,KAAK;YAC1C,OAAO;gBACL,IAAI,CAACC,6BAAL,IAAI,CAACA,2BAA6BC,MAChC,AAAG,IAAI,CAACC,WAAW,GAAC,+BAA4BC,oCAAyB,EACzE;oBAAEC,aAAa;gBAAc,GAE5BV,IAAI,CAAC,CAACW,MAAQA,IAAIC,IAAI,IACtBZ,IAAI,CAAC,CAACC;oBACLE,OAAOC,oBAAoB,GAAGH;oBAC9B,OAAOA,SAASI,KAAK;gBACvB,GACCQ,KAAK,CAAC,CAACC;oBACNC,QAAQC,GAAG,CAAE,qCAAoCF;oBACjD,MAAM,IAAIG,MACR,AAAC,0FACC;gBAEN;gBACF,OAAO,IAAI,CAACX,wBAAwB;YACtC;QACF;IACF;IAEAY,gBAAgB;QACd,IAAItB,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;YACzC,MAAMqB,qBAAqBvB,QAAQC,GAAG,CAACuB,0BAA0B;YACjEjB,OAAOkB,qBAAqB,GAAGF,qBAC1BA,qBACDG;YACJ,OAAOnB,OAAOkB,qBAAqB;QACrC,OAAO;YACL,IAAIlB,OAAOoB,yBAAyB,EAAE;gBACpC,OAAOpB,OAAOoB,yBAAyB;YACzC,OAAO;gBACL,IAAI,CAAC,IAAI,CAACC,0BAA0B,EAAE;oBACpC,2EAA2E;oBAC3E,aAAa;oBACb,IAAI,CAACA,0BAA0B,GAAGjB,MAChC,AAAG,IAAI,CAACC,WAAW,GAAC,mBAAgB,IAAI,CAACiB,OAAO,GAAC,MAAGC,kCAAuB,EAC3E;wBAAEhB,aAAa;oBAAc,GAE5BV,IAAI,CAAC,CAACW,MAAQA,IAAIC,IAAI,IACtBZ,IAAI,CAAC,CAAC2B;wBACLxB,OAAOoB,yBAAyB,GAAGI;wBACnC,OAAOA;oBACT,GACCd,KAAK,CAAC,CAACC;wBACNC,QAAQC,GAAG,CAAE,0CAAyCF;oBACxD;gBACJ;gBACA,wDAAwD;gBACxD,OAAO,IAAI,CAACU,0BAA0B;YACxC;QACF;IACF;IAEAI,YAAYC,MAKX,EAAU;QACT,MAAM,EAAEC,MAAM,EAAEC,IAAI,EAAEC,MAAM,EAAE,GAAGH;QACjC,MAAM,EAAEI,UAAUC,YAAY,EAAEC,KAAK,EAAEC,MAAM,EAAE,GAAGC,IAAAA,kCAAgB,EAACN;QACnE,MAAM,EAAEE,UAAUK,UAAU,EAAE,GAAGD,IAAAA,kCAAgB,EAACP;QAClD,MAAMS,QAAQC,IAAAA,wCAAmB,EAACN;QAClC,IAAIK,KAAK,CAAC,EAAE,KAAK,KAAK;YACpB,MAAM,IAAItB,MAAM,AAAC,8CAA2CsB,QAAM;QACpE;QAEA,MAAME,iBAAiB,CAACC;YACtB,MAAMC,YAAYC,IAAAA,8BAAqB,EACrCJ,IAAAA,wCAAmB,EAACK,IAAAA,oBAAS,EAACH,MAAMV,UACpC;YAEF,OAAOc,IAAAA,wBAAW,EAChB,AAAC,iBAAc,IAAI,CAACrB,OAAO,GAAGkB,YAAYP,QAC1C;QAEJ;QAEA,OAAOK,eACLZ,OAAOkB,iBAAiB,GACpBT,aACAU,IAAAA,yBAAc,EAACT,SACfU,IAAAA,4BAAa,EAACf,cAAcI,YAAYH,OAAOe,MAAM,GACrDX;IAER;IAEAY,OACE,iCAAiC,GACjCZ,KAAa,EACK;QAClB,OAAO,IAAI,CAACa,mBAAmB,CAACpD,IAAI,CAAC,CAACC,WAAaA,SAASoD,GAAG,CAACd;IAClE;IAEAe,SAASf,KAAa,EAA0B;QAC9C,OAAO,IAAI,CAACgB,WAAW,CAACC,SAAS,CAACjB,OAAOvC,IAAI,CAAC,CAACW;YAC7C,IAAI,eAAeA,KAAK;gBACtB,OAAO;oBACL8C,MAAM9C,IAAI+C,SAAS;oBACnBC,KAAKhD,IAAIiD,OAAO;oBAChBC,aAAalD,IAAImD,MAAM,CAACC,GAAG,CAAC,CAACC,IAAO,CAAA;4BAClCjC,MAAMiC,EAAEjC,IAAI;4BACZkC,MAAMD,EAAEE,OAAO;wBACjB,CAAA;gBACF;YACF;YACA,MAAMvD,IAAIwD,KAAK;QACjB;IACF;IAEAC,SAAS7B,KAAa,EAAiB;QACrC,OAAO,IAAI,CAACgB,WAAW,CAACa,QAAQ,CAAC7B;IACnC;IAzIA8B,YAAY5C,OAAe,EAAEjB,WAAmB,CAAE;QAChD,IAAI,CAAC+C,WAAW,GAAGe,IAAAA,8BAAiB,EAAC9D;QAErC,IAAI,CAACiB,OAAO,GAAGA;QACf,IAAI,CAACjB,WAAW,GAAGA;QAEnB,IAAI,CAAC4C,mBAAmB,GAAG,IAAImB,QAAQ,CAACC;YACtC,IAAIrE,OAAOsE,cAAc,EAAE;gBACzBD,QAAQrE,OAAOsE,cAAc;YAC/B,OAAO;gBACLtE,OAAOuE,iBAAiB,GAAG;oBACzBF,QAAQrE,OAAOsE,cAAc;gBAC/B;YACF;QACF;IACF;AA2HF"}