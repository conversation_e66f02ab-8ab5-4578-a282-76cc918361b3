import mongoose, { Document, Schema } from 'mongoose';
import { VALIDATION_RULES } from '@portfolio/shared';

export interface IContactMessage extends Document {
  name: string;
  email: string;
  subject: string;
  message: string;
  isRead: boolean;
  isReplied: boolean;
  ipAddress?: string;
  userAgent?: string;
  createdAt: Date;
  updatedAt: Date;
}

const contactMessageSchema = new Schema<IContactMessage>(
  {
    name: {
      type: String,
      required: [true, 'Name is required'],
      trim: true,
      maxlength: [VALIDATION_RULES.NAME_MAX_LENGTH, `Name cannot be more than ${VALIDATION_RULES.NAME_MAX_LENGTH} characters`],
    },
    email: {
      type: String,
      required: [true, 'Email is required'],
      lowercase: true,
      trim: true,
      maxlength: [VALIDATION_RULES.EMAIL_MAX_LENGTH, `Email cannot be more than ${VALIDATION_RULES.EMAIL_MAX_LENGTH} characters`],
      match: [
        /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/,
        'Please provide a valid email address',
      ],
    },
    subject: {
      type: String,
      required: [true, 'Subject is required'],
      trim: true,
      maxlength: [VALIDATION_RULES.SUBJECT_MAX_LENGTH, `Subject cannot be more than ${VALIDATION_RULES.SUBJECT_MAX_LENGTH} characters`],
    },
    message: {
      type: String,
      required: [true, 'Message is required'],
      trim: true,
      maxlength: [VALIDATION_RULES.MESSAGE_MAX_LENGTH, `Message cannot be more than ${VALIDATION_RULES.MESSAGE_MAX_LENGTH} characters`],
    },
    isRead: {
      type: Boolean,
      default: false,
    },
    isReplied: {
      type: Boolean,
      default: false,
    },
    ipAddress: {
      type: String,
      default: null,
    },
    userAgent: {
      type: String,
      default: null,
    },
  },
  {
    timestamps: true,
    toJSON: {
      transform: function (doc, ret) {
        delete ret.__v;
        return ret;
      },
    },
  }
);

// Indexes for performance
contactMessageSchema.index({ email: 1 });
contactMessageSchema.index({ isRead: 1 });
contactMessageSchema.index({ isReplied: 1 });
contactMessageSchema.index({ createdAt: -1 });

// Static methods
contactMessageSchema.statics.findUnread = function () {
  return this.find({ isRead: false }).sort({ createdAt: -1 });
};

contactMessageSchema.statics.findUnreplied = function () {
  return this.find({ isReplied: false }).sort({ createdAt: -1 });
};

contactMessageSchema.statics.markAsRead = function (id: string) {
  return this.findByIdAndUpdate(id, { isRead: true }, { new: true });
};

contactMessageSchema.statics.markAsReplied = function (id: string) {
  return this.findByIdAndUpdate(id, { isReplied: true }, { new: true });
};

export const ContactMessage = mongoose.model<IContactMessage>('ContactMessage', contactMessageSchema);
