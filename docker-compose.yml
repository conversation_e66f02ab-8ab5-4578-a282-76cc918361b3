version: '3.8'

services:
  # MongoDB Database
  mongodb:
    image: mongo:7.0
    container_name: portfolio-mongodb
    restart: unless-stopped
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: password123
      MONGO_INITDB_DATABASE: portfolio
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
      - ./server/scripts/init-mongo.js:/docker-entrypoint-initdb.d/init-mongo.js:ro
    networks:
      - portfolio-network

  # Redis for caching and sessions
  redis:
    image: redis:7.2-alpine
    container_name: portfolio-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - portfolio-network

  # Backend API Server
  server:
    build:
      context: ./server
      dockerfile: Dockerfile
    container_name: portfolio-server
    restart: unless-stopped
    environment:
      NODE_ENV: development
      PORT: 5000
      MONGODB_URI: ********************************************************************
      REDIS_URL: redis://redis:6379
      JWT_SECRET: your-super-secret-jwt-key-change-in-production
      EMAIL_HOST: smtp.gmail.com
      EMAIL_PORT: 587
      EMAIL_USER: ${EMAIL_USER}
      EMAIL_PASS: ${EMAIL_PASS}
      CLIENT_URL: http://localhost:3000
    ports:
      - "5000:5000"
    volumes:
      - ./server:/app
      - /app/node_modules
    depends_on:
      - mongodb
      - redis
    networks:
      - portfolio-network
    command: npm run dev

  # Frontend Next.js Application
  client:
    build:
      context: ./client
      dockerfile: Dockerfile
    container_name: portfolio-client
    restart: unless-stopped
    environment:
      NODE_ENV: development
      NEXT_PUBLIC_API_URL: http://localhost:5000
      NEXT_PUBLIC_SITE_URL: http://localhost:3000
      NEXT_PUBLIC_GOOGLE_ANALYTICS_ID: ${GOOGLE_ANALYTICS_ID}
    ports:
      - "3000:3000"
    volumes:
      - ./client:/app
      - /app/node_modules
      - /app/.next
    depends_on:
      - server
    networks:
      - portfolio-network
    command: npm run dev

  # Nginx Reverse Proxy (Optional for production)
  nginx:
    image: nginx:alpine
    container_name: portfolio-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
    depends_on:
      - client
      - server
    networks:
      - portfolio-network
    profiles:
      - production

volumes:
  mongodb_data:
    driver: local
  redis_data:
    driver: local

networks:
  portfolio-network:
    driver: bridge
