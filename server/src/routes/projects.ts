import { Router } from 'express';
import { HTTP_STATUS } from '@portfolio/shared';

const router = Router();

// Mock project data
const mockProjects = [
  {
    id: '1',
    title: 'HealthCare Chatbot',
    description: 'Building a 24x7 Healthcare Chatbot using NLP 24x7 support to patients',
    image: '/assets/healthcare.jpg',
    technologies: ['NLP', 'AI', 'Python'],
    githubUrl: 'https://github.com/thestarsahil/Mentalmate',
    featured: true,
    category: 'ai-ml',
    status: 'completed',
  },
  {
    id: '2',
    title: 'Weather WebApp',
    description: 'Web Application using HTML CSS JS with the help of Open Weather API',
    image: '/assets/weather.png',
    technologies: ['HTML', 'CSS', 'JS', 'API'],
    featured: false,
    category: 'web-development',
    status: 'completed',
  },
  {
    id: '3',
    title: 'Contoso Real Estate',
    description: 'Microsoft Real Estate Project allows users to listed properties for sale or rent',
    image: '/assets/real-estate.jpg',
    technologies: ['C#', '.NET', 'Azure'],
    featured: true,
    category: 'web-development',
    status: 'completed',
  },
  {
    id: '4',
    title: 'CropForesight',
    description: 'Assist farmers making smart choices about which crops to grow on their land',
    image: '/assets/crop.jpg',
    technologies: ['ML', 'Python', 'Data'],
    featured: false,
    category: 'ai-ml',
    status: 'completed',
  },
  {
    id: '5',
    title: 'Book Finder',
    description: 'Real-time project that helps users find and purchase books online FREE',
    image: '/assets/book-finder.jpg',
    technologies: ['React', 'Node', 'MongoDB'],
    featured: true,
    category: 'web-development',
    status: 'completed',
  },
];

// Get all projects
router.get('/', (req, res) => {
  const { featured, category, limit } = req.query;
  
  let filteredProjects = [...mockProjects];
  
  if (featured === 'true') {
    filteredProjects = filteredProjects.filter(p => p.featured);
  }
  
  if (category) {
    filteredProjects = filteredProjects.filter(p => p.category === category);
  }
  
  if (limit) {
    filteredProjects = filteredProjects.slice(0, parseInt(limit as string));
  }

  res.status(HTTP_STATUS.OK).json({
    success: true,
    data: filteredProjects,
    timestamp: new Date().toISOString(),
  });
});

// Get featured projects
router.get('/featured', (req, res) => {
  const featuredProjects = mockProjects.filter(p => p.featured);
  
  res.status(HTTP_STATUS.OK).json({
    success: true,
    data: featuredProjects,
    timestamp: new Date().toISOString(),
  });
});

// Get project by ID
router.get('/:id', (req, res) => {
  const project = mockProjects.find(p => p.id === req.params.id);
  
  if (!project) {
    return res.status(HTTP_STATUS.NOT_FOUND).json({
      success: false,
      error: 'Project not found',
      timestamp: new Date().toISOString(),
    });
  }

  res.status(HTTP_STATUS.OK).json({
    success: true,
    data: project,
    timestamp: new Date().toISOString(),
  });
});

export default router;
