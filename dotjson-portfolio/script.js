$(document).ready(function() {
    // Sticky navbar on scroll
    $(window).scroll(function() {
        if(this.scrollY > 20) {
            $('.navbar').addClass("sticky");
        } else {
            $('.navbar').removeClass("sticky");
        }
        if(this.scrollY > 500) {
            $('.scroll-up-btn').addClass("show");
        } else {
            $('.scroll-up-btn').removeClass("show");
        }
    });

    // Slide-up button
    $('.scroll-up-btn').click(function() {
        $('html').animate({scrollTop: 0});
        $('html').css("scrollBehavior", "auto");
    });

    // Toggle menu/navbar
    $('.menu-btn').click(function() {
        $('.navbar .menu').toggleClass("active");
        $('.menu-btn i').toggleClass("active");
    });

    // Typing animation
    if (typeof Typed !== 'undefined') {
        new Typed(".typing", {
            strings: ["C++ Programmer", "Embedded Developer"],
            typeSpeed: 100,
            backSpeed: 60,
            loop: true
        });

        new Typed(".typing-2", {
            strings: ["C++ Programmer", "Embedded Developer"],
            typeSpeed: 100,
            backSpeed: 60,
            loop: true
        });
    } else {
        console.warn('Typed.js is not loaded');
    }

    // Owl Carousel
    $('.carousel').owlCarousel({
        margin: 20,
        loop: true,
        autoplay: true,
        autoplayTimeout: 2000,
        autoplayHoverPause: true,
        responsive: {
            0: {
                items: 1,
                nav: false
            },
            600: {
                items: 2,
                nav: false
            },
            1000: {
                items: 3,
                nav: false
            }
        }
    });

    // Smooth scroll for menu items
    $('.navbar .menu li a').click(function(e) {
        e.preventDefault();
        var targetSection = $(this).attr('href');
        $('html, body').animate({
            scrollTop: $(targetSection).offset().top
        }, 1000);

        // Close mobile menu if open
        $('.navbar .menu').removeClass("active");
        $('.menu-btn i').removeClass("active");
    });

    // Contact form submission with encryption animation
    $('.encrypted-form').submit(function(e) {
        e.preventDefault();

        // Show encryption animation
        const form = $(this);
        const submitBtn = form.find('button');
        const originalBtnText = submitBtn.text();

        // Disable form during animation
        form.find('input, textarea').prop('disabled', true);
        submitBtn.prop('disabled', true).text('Encrypting...');

        // Add encryption animation to each field
        form.find('input, textarea').each(function(index) {
            const field = $(this);
            const originalValue = field.val();
            const delay = index * 300;

            setTimeout(function() {
                // Show encryption animation
                let encryptedText = '';
                for (let i = 0; i < originalValue.length; i++) {
                    encryptedText += '*';
                }
                field.val(encryptedText);

                // Show random characters animation
                setTimeout(function() {
                    const chars = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz!@#$%^&*()_+=-{}[]|:;<>,.?/';
                    let randomText = '';
                    for (let i = 0; i < originalValue.length; i++) {
                        randomText += chars.charAt(Math.floor(Math.random() * chars.length));
                    }
                    field.val(randomText);
                }, 300);
            }, delay);
        });

        // Update button and status
        setTimeout(function() {
            submitBtn.text('Transmitting...');
            $('.encryption-status .status-indicator').text('Transmission in Progress...');
            $('.encryption-icon i').removeClass('fa-shield-alt').addClass('fa-satellite-dish');

            // Complete the animation
            setTimeout(function() {
                submitBtn.text('Message Delivered');
                $('.encryption-status .status-indicator').text('Transmission Complete');
                $('.encryption-icon i').removeClass('fa-satellite-dish').addClass('fa-check-circle');

                // Show success message
                setTimeout(function() {
                    // Reset form
                    form[0].reset();
                    form.find('input, textarea').prop('disabled', false);
                    submitBtn.prop('disabled', false).text(originalBtnText);
                    $('.encryption-status .status-indicator').text('End-to-End Encryption Active');
                    $('.encryption-icon i').removeClass('fa-check-circle').addClass('fa-shield-alt');

                    // Show success message in terminal style
                    const successMessage = $('<div class="terminal-success">Message transmitted securely. Connection terminated.</div>');
                    form.append(successMessage);

                    setTimeout(function() {
                        successMessage.fadeOut(function() {
                            $(this).remove();
                        });
                    }, 3000);
                }, 1500);
            }, 2000);
        }, 1500);
    });

    // PGP key copy animation
    $('.pgp-key').on('click', function() {
        const key = $(this);
        const originalText = key.text();

        // Show copy animation
        key.text('Copying to clipboard...');

        setTimeout(function() {
            key.text('Key copied!');

            setTimeout(function() {
                key.text(originalText);
            }, 1500);
        }, 800);
    });

    // Add typing animation to verification status
    setTimeout(function() {
        const verificationStatus = $('.verification-status');
        const originalText = verificationStatus.text();

        // Clear text and show typing animation
        verificationStatus.html('');
        let charIndex = 0;

        function typeText() {
            if (charIndex < originalText.length) {
                verificationStatus.html(originalText.substring(0, charIndex + 1));
                charIndex++;
                setTimeout(typeText, 50);
            } else {
                // Add check icon at the end
                verificationStatus.html(originalText + ' <i class="fas fa-check-circle"></i>');
            }
        }

        typeText();
    }, 1000);

    // Skills section interactive elements
    $('.skills-btn').on('click', function(e) {
        e.preventDefault();

        // Create a terminal-like popup effect
        const credentialsData = [
            'Accessing secure credentials...',
            'Decrypting data...',
            'Verifying identity...',
            'Access granted!',
            '------------------------',
            'Data Sent to Server:',
            '- Credentials Stored',
            '------------------------',
            'Press any key to close'
        ];

        // Create modal
        const modal = $('<div class="terminal-modal"></div>');
        const modalContent = $('<div class="terminal-modal-content"></div>');
        const closeBtn = $('<span class="terminal-modal-close">&times;</span>');

        modalContent.append(closeBtn);
        modal.append(modalContent);
        $('body').append(modal);

        // Typing effect for credentials
        let lineIndex = 0;
        let charIndex = 0;
        const typingSpeed = 50;
        const lineDelay = 500;

        function typeCredentials() {
            if (lineIndex < credentialsData.length) {
                if (charIndex === 0) {
                    const line = $('<div class="terminal-line-output"></div>');
                    modalContent.append(line);
                }

                const currentLine = modalContent.find('.terminal-line-output').last();
                const currentText = credentialsData[lineIndex].substring(0, charIndex + 1);
                currentLine.text(currentText);

                charIndex++;

                if (charIndex >= credentialsData[lineIndex].length) {
                    lineIndex++;
                    charIndex = 0;
                    setTimeout(typeCredentials, lineDelay);
                } else {
                    setTimeout(typeCredentials, typingSpeed);
                }
            }
        }

        // Start typing effect
        setTimeout(typeCredentials, 500);

        // Close modal on click
        closeBtn.on('click', function() {
            modal.remove();
        });

        // Close on any key press
        $(document).on('keydown.credentials', function() {
            modal.remove();
            $(document).off('keydown.credentials');
        });
    });

    // Add hover effect to skill bars and set dynamic percentages
    $('.bars').each(function() {
        // Get the percentage value from the HTML
        const percentText = $(this).find('.percentage-value').text();

        // Get the line element and its class
        const lineElement = $(this).find('.line');
        const barClass = lineElement.attr('class').split(' ')[1];

        // Create a custom animation for this specific bar using a style tag
        // This is necessary because we can't directly manipulate pseudo-elements with jQuery
        const styleTag = document.createElement('style');
        styleTag.innerHTML = `
            .skills-content .right .${barClass}::before {
                width: ${percentText};
                animation: ${barClass}-fill 2s ease-in-out forwards;
            }

            @keyframes ${barClass}-fill {
                0% { width: 0; }
                100% { width: ${percentText}; }
            }
        `;
        document.head.appendChild(styleTag);
    });

    // Add hover effects to skill bars
    $('.bars').hover(function() {
        $(this).find('.line-progress').css('opacity', '1');
    }, function() {
        $(this).find('.line-progress').css('opacity', '0.5');
    });

    // Add interactive effects to project cards
    $('.project-image-container').on('mouseenter', function() {
        $(this).find('.project-overlay').css('opacity', '1');
        $(this).find('img').css('transform', 'scale(1.1)');
    }).on('mouseleave', function() {
        if (!$(this).closest('.card').is(':hover')) {
            $(this).find('.project-overlay').css('opacity', '0');
            $(this).find('img').css('transform', 'scale(1)');
        }
    });

    // Add glitch effect to dark web banner on click
    $('.dark-web-banner').on('click', function() {
        const banner = $(this);
        banner.addClass('active-glitch');

        // Create a "glitching" effect
        setTimeout(function() {
            banner.removeClass('active-glitch');

            // Change the encrypted text temporarily
            const originalText = $('.encrypted-text').text();
            $('.encrypted-text').text('Decrypting secure data...');

            setTimeout(function() {
                $('.encrypted-text').text(originalText);
            }, 2000);

        }, 1000);
    });

    // Add random tech tag color change on interval
    setInterval(function() {
        const randomTag = $('.tech-tag').eq(Math.floor(Math.random() * $('.tech-tag').length));
        randomTag.addClass('tag-highlight');

        setTimeout(function() {
            randomTag.removeClass('tag-highlight');
        }, 1000);
    }, 3000);

    // Footer landscape interactions
    $('.footer-message .terminal-line').on('click', function() {
        // Create a terminal shutdown effect
        const footerMessage = $('.footer-message');

        // Add glitch effect
        footerMessage.find('.glitch-text').addClass('active-glitch');

        // Change text to simulate shutdown
        setTimeout(function() {
            footerMessage.find('.encrypted-text').text('Disconnecting from network...');

            setTimeout(function() {
                footerMessage.find('.encrypted-text').text('Erasing connection logs...');

                setTimeout(function() {
                    footerMessage.find('.encrypted-text').text('Connection terminated safely');
                    footerMessage.find('.glitch-text').removeClass('active-glitch');

                    // Add a visual effect to the image
                    $('.footer-landscape-img').css({
                        'filter': 'grayscale(80%) brightness(0.5) contrast(1.5)',
                        'transform': 'scale(1.1)',
                        'object-position': 'center 25%' // Show more of the eyes during the effect
                    });

                    setTimeout(function() {
                        // Reset the image effect
                        $('.footer-landscape-img').css({
                            'filter': 'grayscale(30%) brightness(0.85) contrast(1.1)',
                            'transform': 'scale(1)',
                            'object-position': 'center 30%' // Return to original position
                        });
                    }, 2000);

                }, 1000);
            }, 1000);
        }, 1000);
    });

    // Add parallax effect to footer landscape
    $(window).on('scroll', function() {
        const scrollPosition = $(window).scrollTop();
        const windowHeight = $(window).height();
        const documentHeight = $(document).height();

        // Only apply effect when near the footer
        if (scrollPosition > documentHeight - windowHeight - 300) {
            const scrollPercentage = (scrollPosition + windowHeight) / documentHeight;
            const translateY = (scrollPercentage * 50) - 25; // -25% to 25% movement

            $('.footer-landscape-img').css({
                'transform': `translateY(${translateY}px) scale(1.05)`,
                'object-position': `center calc(30% + ${translateY/2}px)`
            });
        }
    });
});

// Three.js is initialized in three-background.js
