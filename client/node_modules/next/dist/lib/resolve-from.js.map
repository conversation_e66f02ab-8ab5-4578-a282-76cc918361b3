{"version": 3, "sources": ["../../src/lib/resolve-from.ts"], "names": ["resolveFrom", "<PERSON><PERSON><PERSON>", "require", "fromDirectory", "moduleId", "silent", "TypeError", "realpathSync", "error", "isError", "code", "path", "resolve", "fromFile", "join", "resolveFileName", "_resolveFilename", "id", "filename", "paths", "_nodeModulePaths"], "mappings": "AAAA,uDAAuD;;;;;+BAO1CA;;;eAAAA;;;6DANI;gEACG;0BACS;;;;;;AAE7B,MAAMC,SAASC,QAAQ;AAEhB,MAAMF,cAAc,CACzBG,eACAC,UACAC;IAEA,IAAI,OAAOF,kBAAkB,UAAU;QACrC,MAAM,IAAIG,UACR,CAAC,qDAAqD,EAAE,OAAOH,cAAc,EAAE,CAAC;IAEpF;IAEA,IAAI,OAAOC,aAAa,UAAU;QAChC,MAAM,IAAIE,UACR,CAAC,sDAAsD,EAAE,OAAOF,SAAS,EAAE,CAAC;IAEhF;IAEA,IAAI;QACFD,gBAAgBI,IAAAA,sBAAY,EAACJ;IAC/B,EAAE,OAAOK,OAAgB;QACvB,IAAIC,IAAAA,gBAAO,EAACD,UAAUA,MAAME,IAAI,KAAK,UAAU;YAC7CP,gBAAgBQ,aAAI,CAACC,OAAO,CAACT;QAC/B,OAAO,IAAIE,QAAQ;YACjB;QACF,OAAO;YACL,MAAMG;QACR;IACF;IAEA,MAAMK,WAAWF,aAAI,CAACG,IAAI,CAACX,eAAe;IAE1C,MAAMY,kBAAkB,IACtBd,OAAOe,gBAAgB,CAACZ,UAAU;YAChCa,IAAIJ;YACJK,UAAUL;YACVM,OAAOlB,OAAOmB,gBAAgB,CAACjB;QACjC;IAEF,IAAIE,QAAQ;QACV,IAAI;YACF,OAAOU;QACT,EAAE,OAAOP,OAAO;YACd;QACF;IACF;IAEA,OAAOO;AACT"}