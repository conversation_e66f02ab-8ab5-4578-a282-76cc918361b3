'use client';

import { useEffect } from 'react';
import { usePathname } from 'next/navigation';

declare global {
  interface Window {
    gtag: (command: string, targetId: string, config?: any) => void;
  }
}

export function Analytics() {
  const pathname = usePathname();
  const GA_TRACKING_ID = process.env.NEXT_PUBLIC_GOOGLE_ANALYTICS_ID;

  useEffect(() => {
    if (!GA_TRACKING_ID) return;

    // Load Google Analytics script
    const script = document.createElement('script');
    script.src = `https://www.googletagmanager.com/gtag/js?id=${GA_TRACKING_ID}`;
    script.async = true;
    document.head.appendChild(script);

    // Initialize gtag
    window.gtag = function gtag() {
      // eslint-disable-next-line prefer-rest-params
      (window as any).dataLayer = (window as any).dataLayer || [];
      // eslint-disable-next-line prefer-rest-params
      (window as any).dataLayer.push(arguments);
    };

    window.gtag('js', new Date());
    window.gtag('config', GA_TRACKING_ID, {
      page_path: pathname,
    });

    return () => {
      document.head.removeChild(script);
    };
  }, [GA_TRACKING_ID]);

  useEffect(() => {
    if (!GA_TRACKING_ID || !window.gtag) return;

    window.gtag('config', GA_TRACKING_ID, {
      page_path: pathname,
    });
  }, [pathname, GA_TRACKING_ID]);

  return null;
}

// Analytics tracking functions
export const trackEvent = (action: string, category: string, label?: string, value?: number) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', action, {
      event_category: category,
      event_label: label,
      value: value,
    });
  }
};

export const trackPageView = (url: string) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('config', process.env.NEXT_PUBLIC_GOOGLE_ANALYTICS_ID!, {
      page_path: url,
    });
  }
};

export const trackContactForm = () => {
  trackEvent('submit', 'contact_form', 'contact_page');
};

export const trackProjectView = (projectName: string) => {
  trackEvent('view', 'project', projectName);
};

export const trackResumeDownload = () => {
  trackEvent('download', 'resume', 'header_button');
};

export const trackSocialClick = (platform: string) => {
  trackEvent('click', 'social_media', platform);
};
