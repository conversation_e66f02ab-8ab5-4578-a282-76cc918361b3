name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    services:
      mongodb:
        image: mongo:7.0
        env:
          MONGO_INITDB_ROOT_USERNAME: admin
          MONGO_INITDB_ROOT_PASSWORD: password123
        ports:
          - 27017:27017
        options: >-
          --health-cmd "mongosh --eval 'db.adminCommand(\"ping\")'"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

      redis:
        image: redis:7.2-alpine
        ports:
          - 6379:6379
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    strategy:
      matrix:
        node-version: [18.x, 20.x]

    steps:
    - uses: actions/checkout@v4

    - name: Use Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v4
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Build shared package
      run: |
        cd shared
        npm run build

    - name: Lint client
      run: |
        cd client
        npm run lint

    - name: Lint server
      run: |
        cd server
        npm run lint

    - name: Type check client
      run: |
        cd client
        npm run type-check

    - name: Type check server
      run: |
        cd server
        npm run type-check

    - name: Test server
      run: |
        cd server
        npm test
      env:
        NODE_ENV: test
        MONGODB_URI: ***************************************************************************
        REDIS_URL: redis://localhost:6379
        JWT_SECRET: test-jwt-secret-key-for-testing-purposes-only
        EMAIL_USER: <EMAIL>
        EMAIL_PASS: test-password
        SESSION_SECRET: test-session-secret
        ADMIN_EMAIL: <EMAIL>
        ADMIN_PASSWORD: test-admin-password

    - name: Build client
      run: |
        cd client
        npm run build
      env:
        NEXT_PUBLIC_API_URL: http://localhost:5000
        NEXT_PUBLIC_SITE_URL: http://localhost:3000

    - name: Build server
      run: |
        cd server
        npm run build

  deploy:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'

    steps:
    - uses: actions/checkout@v4

    - name: Deploy to production
      run: |
        echo "Deployment steps would go here"
        echo "This could include:"
        echo "- Building Docker images"
        echo "- Pushing to container registry"
        echo "- Deploying to cloud provider"
        echo "- Running database migrations"
