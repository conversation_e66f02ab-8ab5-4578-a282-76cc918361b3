'use client';

import { useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import { Terminal, Download } from 'lucide-react';

const roles = ['C++ Programmer', 'Embedded Developer', 'Full Stack Developer', 'Cybersecurity Expert'];

export function Hero() {
  const [currentRole, setCurrentRole] = useState(0);
  const [displayText, setDisplayText] = useState('');
  const [isDeleting, setIsDeleting] = useState(false);

  useEffect(() => {
    const role = roles[currentRole];
    const timeout = setTimeout(() => {
      if (!isDeleting) {
        if (displayText.length < role.length) {
          setDisplayText(role.slice(0, displayText.length + 1));
        } else {
          setTimeout(() => setIsDeleting(true), 2000);
        }
      } else {
        if (displayText.length > 0) {
          setDisplayText(displayText.slice(0, -1));
        } else {
          setIsDeleting(false);
          setCurrentRole((prev) => (prev + 1) % roles.length);
        }
      }
    }, isDeleting ? 50 : 100);

    return () => clearTimeout(timeout);
  }, [displayText, isDeleting, currentRole]);

  return (
    <section id="home" className="min-h-screen flex items-center justify-center section-padding">
      <div className="container-custom">
        <div className="max-w-4xl mx-auto text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="terminal-window max-w-3xl mx-auto"
          >
            {/* Terminal Header */}
            <div className="terminal-header">
              <div className="terminal-buttons">
                <span className="terminal-button close"></span>
                <span className="terminal-button minimize"></span>
                <span className="terminal-button maximize"></span>
              </div>
              <div className="terminal-title glitch-text" data-text="kali@sahil: ~/portfolio">
                kali@sahil: ~/portfolio
              </div>
            </div>

            {/* Terminal Body */}
            <div className="terminal-body text-left space-y-4">
              <div className="terminal-line">whoami</div>
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.5, duration: 0.8 }}
                className="text-2xl md:text-3xl font-bold text-darkweb-text mb-4"
              >
                Hello, I am Sahil Ali
              </motion.div>

              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 1, duration: 0.8 }}
                className="text-xl md:text-2xl text-darkweb-accent mb-6 glitch-text"
                data-text="Welcome to Dark Reality"
              >
                Welcome to Dark Reality
              </motion.div>

              <div className="terminal-line">profession</div>
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 1.5, duration: 0.8 }}
                className="text-lg md:text-xl text-darkweb-text mb-6"
              >
                I'm a{' '}
                <span className="text-darkweb-accent font-bold">
                  {displayText}
                  <span className="animate-blink">|</span>
                </span>
              </motion.div>

              <div className="terminal-line">contact --hire</div>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 2, duration: 0.8 }}
                className="flex flex-col sm:flex-row gap-4 mt-8"
              >
                <button
                  onClick={() => {
                    const element = document.querySelector('#contact');
                    if (element) {
                      element.scrollIntoView({ behavior: 'smooth' });
                    }
                  }}
                  className="btn-terminal flex items-center justify-center gap-2"
                >
                  <Terminal className="w-4 h-4" />
                  Hire me
                </button>
                
                <a
                  href="/Resume Sahil 2024.pdf"
                  download
                  className="btn-secondary flex items-center justify-center gap-2"
                >
                  <Download className="w-4 h-4" />
                  Download CV
                </a>
              </motion.div>
            </div>
          </motion.div>

          {/* Floating Elements */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 2.5, duration: 1 }}
            className="absolute top-1/4 left-10 text-darkweb-green font-mono text-sm opacity-30 animate-float"
          >
            {'> Initializing...'}
          </motion.div>

          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 3, duration: 1 }}
            className="absolute top-1/3 right-10 text-darkweb-purple font-mono text-sm opacity-30 animate-float animate-delay-200"
          >
            {'[SECURE CONNECTION]'}
          </motion.div>

          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 3.5, duration: 1 }}
            className="absolute bottom-1/4 left-1/4 text-darkweb-accent font-mono text-sm opacity-30 animate-float animate-delay-500"
          >
            {'$ sudo access_granted'}
          </motion.div>
        </div>
      </div>
    </section>
  );
}
