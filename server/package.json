{"name": "@portfolio/server", "version": "2.0.0", "description": "Node.js/Express backend API for <PERSON><PERSON>'s portfolio", "main": "dist/server.js", "scripts": {"dev": "nodemon src/server.ts", "build": "tsc", "start": "node dist/server.js", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "type-check": "tsc --noEmit", "clean": "rm -rf dist", "seed": "ts-node src/scripts/seed.ts"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "compression": "^1.7.4", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "mongoose": "^8.0.3", "redis": "^4.6.11", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "nodemailer": "^6.9.7", "multer": "^1.4.5-lts.1", "sharp": "^0.33.1", "dotenv": "^16.3.1", "winston": "^3.11.0", "express-winston": "^4.2.0", "@portfolio/shared": "workspace:*", "joi": "^17.11.0", "cookie-parser": "^1.4.6", "express-session": "^1.17.3", "connect-redis": "^7.1.0", "passport": "^0.7.0", "passport-local": "^1.0.0", "passport-jwt": "^4.0.1", "slugify": "^1.6.6", "marked": "^11.1.1", "dompurify": "^3.0.7", "jsdom": "^23.0.1", "node-cron": "^3.0.3", "axios": "^1.6.2"}, "devDependencies": {"@types/node": "^20.10.5", "@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/morgan": "^1.9.9", "@types/compression": "^1.7.5", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/nodemailer": "^6.4.14", "@types/multer": "^1.4.11", "@types/cookie-parser": "^1.4.6", "@types/express-session": "^1.17.10", "@types/passport": "^1.0.16", "@types/passport-local": "^1.0.38", "@types/passport-jwt": "^3.0.13", "@types/marked": "^6.0.0", "@types/dompurify": "^3.0.5", "@types/jsdom": "^21.1.6", "@types/node-cron": "^3.0.11", "typescript": "^5.3.3", "nodemon": "^3.0.2", "ts-node": "^10.9.2", "eslint": "^8.56.0", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "prettier": "^3.1.1", "jest": "^29.7.0", "@types/jest": "^29.5.11", "ts-jest": "^29.1.1", "supertest": "^6.3.3", "@types/supertest": "^6.0.2"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}