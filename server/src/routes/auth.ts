import { Router } from 'express';
import { body } from 'express-validator';
import { authenticate } from '@/middleware/auth';
import { HTTP_STATUS } from '@portfolio/shared';

const router = Router();

// Placeholder auth routes
router.post('/login', [
  body('email').isEmail().withMessage('Please provide a valid email'),
  body('password').isLength({ min: 8 }).withMessage('Password must be at least 8 characters'),
], (req, res) => {
  res.status(HTTP_STATUS.OK).json({
    success: true,
    message: 'Auth endpoints coming soon',
    timestamp: new Date().toISOString(),
  });
});

router.post('/logout', authenticate, (req, res) => {
  res.status(HTTP_STATUS.OK).json({
    success: true,
    message: 'Logged out successfully',
    timestamp: new Date().toISOString(),
  });
});

router.get('/me', authenticate, (req, res) => {
  res.status(HTTP_STATUS.OK).json({
    success: true,
    data: req.user,
    timestamp: new Date().toISOString(),
  });
});

export default router;
