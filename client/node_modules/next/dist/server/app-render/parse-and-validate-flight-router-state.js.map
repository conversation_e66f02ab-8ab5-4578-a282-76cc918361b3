{"version": 3, "sources": ["../../../src/server/app-render/parse-and-validate-flight-router-state.tsx"], "names": ["parseAndValidateFlightRouterState", "<PERSON><PERSON><PERSON><PERSON>", "undefined", "Array", "isArray", "Error", "length", "state", "JSON", "parse", "decodeURIComponent", "assert", "flightRouterStateSchema"], "mappings": ";;;;+BAIgBA;;;eAAAA;;;uBAHwB;6BACjB;AAEhB,SAASA,kCACdC,WAA0C;IAE1C,IAAI,OAAOA,gBAAgB,aAAa;QACtC,OAAOC;IACT;IACA,IAAIC,MAAMC,OAAO,CAACH,cAAc;QAC9B,MAAM,IAAII,MACR;IAEJ;IAEA,4EAA4E;IAC5E,yEAAyE;IACzE,iCAAiC;IACjC,gEAAgE;IAChE,wCAAwC;IACxC,IAAIJ,YAAYK,MAAM,GAAG,KAAK,MAAM;QAClC,MAAM,IAAID,MAAM;IAClB;IAEA,IAAI;QACF,MAAME,QAAQC,KAAKC,KAAK,CAACC,mBAAmBT;QAC5CU,IAAAA,mBAAM,EAACJ,OAAOK,8BAAuB;QACrC,OAAOL;IACT,EAAE,OAAM;QACN,MAAM,IAAIF,MAAM;IAClB;AACF"}