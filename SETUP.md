# Portfolio Setup Guide

This guide will help you set up the modern client-server portfolio application.

## 🚀 Quick Setup (Recommended)

### Using Docker (Easiest)

1. **Prerequisites**
   - Docker and Docker Compose installed
   - Git

2. **<PERSON>lone and Start**
   ```bash
   git clone https://github.com/thestarsahil/Portfolio.git
   cd Portfolio
   cp .env.example .env
   cp client/.env.example client/.env.local
   cp server/.env.example server/.env
   
   # Edit environment files with your values
   # At minimum, set email credentials in server/.env
   
   docker-compose up -d
   ```

3. **Access**
   - Portfolio: http://localhost:3000
   - API: http://localhost:5000
   - Health: http://localhost:5000/health

## 🔧 Manual Setup

### 1. Prerequisites

- **Node.js 18+** - [Download](https://nodejs.org/)
- **MongoDB** - [Install Guide](https://docs.mongodb.com/manual/installation/)
- **Redis** (optional) - [Install Guide](https://redis.io/download)
- **Git** - [Download](https://git-scm.com/)

### 2. Clone Repository

```bash
git clone https://github.com/thestarsahil/Portfolio.git
cd Portfolio
```

### 3. Install Dependencies

```bash
# Install all workspace dependencies
npm install

# Or install individually
cd shared && npm install
cd ../client && npm install
cd ../server && npm install
```

### 4. Environment Configuration

#### Root Environment (.env)
```bash
cp .env.example .env
```

Edit `.env` with your values:
```env
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password
GOOGLE_ANALYTICS_ID=G-XXXXXXXXXX
MONGODB_URI=mongodb://localhost:27017/portfolio
JWT_SECRET=your-super-secret-jwt-key-minimum-32-characters
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=your-secure-admin-password
```

#### Client Environment (client/.env.local)
```bash
cd client
cp .env.example .env.local
```

Edit `client/.env.local`:
```env
NEXT_PUBLIC_API_URL=http://localhost:5000
NEXT_PUBLIC_SITE_URL=http://localhost:3000
NEXT_PUBLIC_GOOGLE_ANALYTICS_ID=G-XXXXXXXXXX
```

#### Server Environment (server/.env)
```bash
cd server
cp .env.example .env
```

Edit `server/.env` with the same values as root `.env`.

### 5. Database Setup

#### Option A: Local MongoDB
```bash
# Start MongoDB service
sudo systemctl start mongod  # Linux
brew services start mongodb-community  # macOS

# Start Redis (optional)
redis-server
```

#### Option B: MongoDB Atlas (Cloud)
1. Create account at [MongoDB Atlas](https://www.mongodb.com/atlas)
2. Create cluster and get connection string
3. Update `MONGODB_URI` in environment files

### 6. Seed Database

```bash
cd server
npm run seed
```

This creates:
- Admin user with credentials from environment
- Sample projects
- Database indexes

### 7. Start Development

#### Option A: All Services
```bash
# From root directory
npm run dev
```

#### Option B: Individual Services
```bash
# Terminal 1 - Client
cd client && npm run dev

# Terminal 2 - Server
cd server && npm run dev
```

### 8. Verify Setup

- **Frontend**: http://localhost:3000
- **API Health**: http://localhost:5000/health
- **Contact Form**: Test the contact form
- **Admin Login**: Use admin credentials from environment

## 📧 Email Configuration

### Gmail Setup (Recommended)

1. **Enable 2FA** on your Google account
2. **Generate App Password**:
   - Go to Google Account settings
   - Security → 2-Step Verification → App passwords
   - Generate password for "Mail"
3. **Update Environment**:
   ```env
   EMAIL_USER=<EMAIL>
   EMAIL_PASS=your-16-character-app-password
   ```

### Other Email Providers

Update `server/.env`:
```env
EMAIL_HOST=smtp.your-provider.com
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=your-email
EMAIL_PASS=your-password
```

## 🔐 Security Setup

### JWT Secret
Generate a secure JWT secret:
```bash
node -e "console.log(require('crypto').randomBytes(32).toString('hex'))"
```

### Admin Account
Set strong admin credentials:
```env
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=your-very-secure-password
ADMIN_NAME=Your Name
```

## 🚀 Production Deployment

### Environment Variables
Set production values:
```env
NODE_ENV=production
CLIENT_URL=https://yourdomain.com
ALLOWED_ORIGINS=https://yourdomain.com
MONGODB_URI=mongodb+srv://user:<EMAIL>/portfolio
```

### Docker Production
```bash
# Build production images
docker-compose -f docker-compose.yml -f docker-compose.prod.yml build

# Start production services
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d
```

### Manual Production
```bash
# Build applications
npm run build

# Start production servers
npm run start
```

## 🧪 Testing

```bash
# Run all tests
npm test

# Test individual services
cd client && npm test
cd server && npm test

# Test with coverage
npm run test:coverage
```

## 🔍 Troubleshooting

### Common Issues

1. **Port Already in Use**
   ```bash
   # Kill processes on ports
   lsof -ti:3000 | xargs kill -9
   lsof -ti:5000 | xargs kill -9
   ```

2. **MongoDB Connection Failed**
   - Check MongoDB is running: `mongosh`
   - Verify connection string in environment
   - Check firewall settings

3. **Email Not Sending**
   - Verify email credentials
   - Check app password (not regular password)
   - Test with simple SMTP client

4. **Build Errors**
   ```bash
   # Clean and reinstall
   npm run clean
   rm -rf node_modules package-lock.json
   npm install
   ```

### Logs

```bash
# View application logs
docker-compose logs -f

# View specific service logs
docker-compose logs -f client
docker-compose logs -f server
```

## 📚 Additional Resources

- [Next.js Documentation](https://nextjs.org/docs)
- [Express.js Guide](https://expressjs.com/en/guide/routing.html)
- [MongoDB Manual](https://docs.mongodb.com/manual/)
- [Docker Compose Reference](https://docs.docker.com/compose/)

## 🆘 Support

If you encounter issues:

1. Check this setup guide
2. Review error logs
3. Check environment configuration
4. Verify all services are running
5. Create an issue on GitHub with:
   - Error message
   - Environment details
   - Steps to reproduce
