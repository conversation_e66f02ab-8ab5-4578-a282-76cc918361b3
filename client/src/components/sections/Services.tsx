'use client';

import { motion } from 'framer-motion';
import { Award, Bot, Code, Zap } from 'lucide-react';

const services = [
  {
    icon: Award,
    title: 'Fastest Coder by Microsoft',
    description: 'Build a Finance App with JS and Github Copilot',
    color: 'text-blue-400',
  },
  {
    icon: Bot,
    title: 'AI/ML Contributor',
    description: 'Contributing to various projects related to AI, React, Cloud',
    color: 'text-darkweb-accent',
  },
  {
    icon: Code,
    title: 'NLP Model Contributor',
    description: 'Contributing to various projects related to AI, React, Cloud',
    color: 'text-darkweb-purple',
  },
];

export function Services() {
  return (
    <section id="services" className="section-padding">
      <div className="container-custom">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl md:text-4xl font-bold text-darkweb-text mb-4">
            Hackathon and{' '}
            <span className="text-darkweb-accent glitch-text" data-text="Open Source">
              Open Source
            </span>{' '}
            Contributor
          </h2>
          <div className="w-24 h-1 bg-darkweb-accent mx-auto"></div>
        </motion.div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {services.map((service, index) => (
            <motion.div
              key={service.title}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: index * 0.2 }}
              viewport={{ once: true }}
              className="card-glow group"
            >
              <div className="text-center space-y-4">
                <div className={`inline-flex p-4 rounded-full bg-darkweb-surface ${service.color} group-hover:scale-110 transition-transform duration-300`}>
                  <service.icon className="w-8 h-8" />
                </div>
                
                <h3 className="text-xl font-bold text-darkweb-text group-hover:text-darkweb-accent transition-colors">
                  {service.title}
                </h3>
                
                <p className="text-darkweb-muted leading-relaxed">
                  {service.description}
                </p>
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
}
