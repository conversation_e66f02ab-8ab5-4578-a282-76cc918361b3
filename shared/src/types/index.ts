// Shared types for the portfolio application

export interface Project {
  id: string;
  title: string;
  description: string;
  longDescription?: string;
  image: string;
  images?: string[];
  technologies: string[];
  githubUrl?: string;
  liveUrl?: string;
  featured: boolean;
  category: ProjectCategory;
  status: ProjectStatus;
  startDate: Date;
  endDate?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export enum ProjectCategory {
  WEB_DEVELOPMENT = 'web-development',
  MOBILE_DEVELOPMENT = 'mobile-development',
  AI_ML = 'ai-ml',
  CYBERSECURITY = 'cybersecurity',
  EMBEDDED_SYSTEMS = 'embedded-systems',
  DATA_SCIENCE = 'data-science',
  BLOCKCHAIN = 'blockchain',
  GAME_DEVELOPMENT = 'game-development',
  OTHER = 'other'
}

export enum ProjectStatus {
  PLANNING = 'planning',
  IN_PROGRESS = 'in-progress',
  COMPLETED = 'completed',
  MAINTENANCE = 'maintenance',
  ARCHIVED = 'archived'
}

export interface ContactMessage {
  id: string;
  name: string;
  email: string;
  subject: string;
  message: string;
  isRead: boolean;
  isReplied: boolean;
  ipAddress?: string;
  userAgent?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface Skill {
  id: string;
  name: string;
  category: SkillCategory;
  proficiency: number; // 0-100
  icon?: string;
  description?: string;
  yearsOfExperience?: number;
  certifications?: string[];
  projects?: string[]; // Project IDs
}

export enum SkillCategory {
  PROGRAMMING_LANGUAGES = 'programming-languages',
  FRAMEWORKS = 'frameworks',
  DATABASES = 'databases',
  TOOLS = 'tools',
  CLOUD_PLATFORMS = 'cloud-platforms',
  CYBERSECURITY = 'cybersecurity',
  AI_ML = 'ai-ml',
  SOFT_SKILLS = 'soft-skills'
}

export interface Experience {
  id: string;
  company: string;
  position: string;
  description: string;
  startDate: Date;
  endDate?: Date;
  location: string;
  type: ExperienceType;
  skills: string[];
  achievements: string[];
  companyUrl?: string;
  companyLogo?: string;
}

export enum ExperienceType {
  FULL_TIME = 'full-time',
  PART_TIME = 'part-time',
  INTERNSHIP = 'internship',
  FREELANCE = 'freelance',
  CONTRACT = 'contract',
  VOLUNTEER = 'volunteer'
}

export interface Education {
  id: string;
  institution: string;
  degree: string;
  field: string;
  startDate: Date;
  endDate?: Date;
  gpa?: number;
  description?: string;
  achievements?: string[];
  institutionUrl?: string;
  institutionLogo?: string;
}

export interface BlogPost {
  id: string;
  title: string;
  slug: string;
  excerpt: string;
  content: string;
  featuredImage?: string;
  tags: string[];
  category: BlogCategory;
  published: boolean;
  publishedAt?: Date;
  readTime: number; // in minutes
  views: number;
  likes: number;
  createdAt: Date;
  updatedAt: Date;
}

export enum BlogCategory {
  TECHNOLOGY = 'technology',
  CYBERSECURITY = 'cybersecurity',
  AI_ML = 'ai-ml',
  WEB_DEVELOPMENT = 'web-development',
  CAREER = 'career',
  TUTORIALS = 'tutorials',
  PERSONAL = 'personal'
}

export interface Analytics {
  id: string;
  event: AnalyticsEvent;
  page: string;
  userAgent?: string;
  ipAddress?: string;
  country?: string;
  city?: string;
  referrer?: string;
  sessionId?: string;
  userId?: string;
  metadata?: Record<string, any>;
  timestamp: Date;
}

export enum AnalyticsEvent {
  PAGE_VIEW = 'page_view',
  CONTACT_FORM_SUBMIT = 'contact_form_submit',
  PROJECT_VIEW = 'project_view',
  RESUME_DOWNLOAD = 'resume_download',
  SOCIAL_LINK_CLICK = 'social_link_click',
  BLOG_POST_VIEW = 'blog_post_view',
  SKILL_HOVER = 'skill_hover',
  TERMINAL_COMMAND = 'terminal_command'
}

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  timestamp: Date;
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

export interface User {
  id: string;
  email: string;
  name: string;
  role: UserRole;
  avatar?: string;
  isActive: boolean;
  lastLogin?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export enum UserRole {
  ADMIN = 'admin',
  MODERATOR = 'moderator',
  USER = 'user'
}

export interface AuthTokens {
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
}

export interface SystemStats {
  totalProjects: number;
  totalMessages: number;
  totalViews: number;
  totalBlogPosts: number;
  recentActivity: Analytics[];
  popularProjects: Project[];
  topSkills: Skill[];
}

// API Request/Response types
export interface ContactFormData {
  name: string;
  email: string;
  subject: string;
  message: string;
}

export interface ProjectCreateData {
  title: string;
  description: string;
  longDescription?: string;
  image: string;
  images?: string[];
  technologies: string[];
  githubUrl?: string;
  liveUrl?: string;
  featured: boolean;
  category: ProjectCategory;
  status: ProjectStatus;
  startDate: Date;
  endDate?: Date;
}

export interface SkillCreateData {
  name: string;
  category: SkillCategory;
  proficiency: number;
  icon?: string;
  description?: string;
  yearsOfExperience?: number;
  certifications?: string[];
}

// Utility types
export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;
export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;
export type PartialExcept<T, K extends keyof T> = Partial<T> & Pick<T, K>;
