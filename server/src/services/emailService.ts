import nodemailer from 'nodemailer';
import { config } from '@/config/environment';
import { logger } from '@/utils/logger';

interface ContactNotificationData {
  name: string;
  email: string;
  subject: string;
  message: string;
  contactMessageId: string;
}

interface ContactConfirmationData {
  name: string;
  email: string;
  subject: string;
}

class EmailService {
  private transporter: nodemailer.Transporter;

  constructor() {
    this.transporter = nodemailer.createTransporter({
      host: config.emailHost,
      port: config.emailPort,
      secure: config.emailSecure,
      auth: {
        user: config.emailUser,
        pass: config.emailPass,
      },
    });

    // Verify connection configuration
    this.verifyConnection();
  }

  private async verifyConnection(): Promise<void> {
    try {
      await this.transporter.verify();
      logger.info('Email service connected successfully');
    } catch (error) {
      logger.error('Email service connection failed:', error);
    }
  }

  async sendContactNotification(data: ContactNotificationData): Promise<void> {
    const { name, email, subject, message, contactMessageId } = data;

    const htmlContent = `
      <div style="font-family: 'Fira Code', monospace; background-color: #0a0a0a; color: #00ffff; padding: 20px;">
        <div style="border: 1px solid #333; border-radius: 8px; padding: 20px; background-color: #121212;">
          <h2 style="color: #00ffff; margin-bottom: 20px;">🔒 New Contact Message Received</h2>
          
          <div style="background-color: #1a1a1a; border: 1px solid #333; border-radius: 4px; padding: 15px; margin-bottom: 15px;">
            <h3 style="color: #6e0b75; margin-bottom: 10px;">Message Details:</h3>
            <p><strong>Name:</strong> ${name}</p>
            <p><strong>Email:</strong> <a href="mailto:${email}" style="color: #00ffff;">${email}</a></p>
            <p><strong>Subject:</strong> ${subject}</p>
            <p><strong>Message ID:</strong> ${contactMessageId}</p>
          </div>
          
          <div style="background-color: #1a1a1a; border: 1px solid #333; border-radius: 4px; padding: 15px;">
            <h3 style="color: #6e0b75; margin-bottom: 10px;">Message:</h3>
            <p style="white-space: pre-wrap; line-height: 1.6;">${message}</p>
          </div>
          
          <div style="margin-top: 20px; padding-top: 15px; border-top: 1px solid #333;">
            <p style="font-size: 12px; color: #888;">
              This message was sent from your portfolio contact form.
              <br>
              Time: ${new Date().toLocaleString()}
            </p>
          </div>
        </div>
      </div>
    `;

    const textContent = `
New Contact Message Received

Name: ${name}
Email: ${email}
Subject: ${subject}
Message ID: ${contactMessageId}

Message:
${message}

Time: ${new Date().toLocaleString()}
    `;

    await this.transporter.sendMail({
      from: config.emailFrom,
      to: config.adminEmail,
      subject: `🔒 New Contact: ${subject}`,
      text: textContent,
      html: htmlContent,
    });

    logger.info(`Contact notification email sent for message ${contactMessageId}`);
  }

  async sendContactConfirmation(data: ContactConfirmationData): Promise<void> {
    const { name, email, subject } = data;

    const htmlContent = `
      <div style="font-family: 'Fira Code', monospace; background-color: #0a0a0a; color: #00ffff; padding: 20px;">
        <div style="border: 1px solid #333; border-radius: 8px; padding: 20px; background-color: #121212;">
          <h2 style="color: #00ffff; margin-bottom: 20px;">✅ Message Transmitted Successfully</h2>
          
          <p>Hello <strong style="color: #6e0b75;">${name}</strong>,</p>
          
          <p>Your message has been successfully transmitted through our secure communication channel.</p>
          
          <div style="background-color: #1a1a1a; border: 1px solid #333; border-radius: 4px; padding: 15px; margin: 20px 0;">
            <h3 style="color: #6e0b75; margin-bottom: 10px;">Transmission Details:</h3>
            <p><strong>Subject:</strong> ${subject}</p>
            <p><strong>Status:</strong> <span style="color: #00ff00;">DELIVERED</span></p>
            <p><strong>Encryption:</strong> <span style="color: #00ff00;">ACTIVE</span></p>
            <p><strong>Time:</strong> ${new Date().toLocaleString()}</p>
          </div>
          
          <p>I'll review your message and respond as soon as possible. Thank you for reaching out!</p>
          
          <div style="margin-top: 30px; padding-top: 15px; border-top: 1px solid #333;">
            <p style="color: #6e0b75; font-weight: bold;">Sahil Ali</p>
            <p style="font-size: 14px; color: #888;">
              Full Stack Developer & Cybersecurity Expert<br>
              <a href="https://thestarsahil.me" style="color: #00ffff;">thestarsahil.me</a>
            </p>
          </div>
          
          <div style="margin-top: 20px; padding-top: 15px; border-top: 1px solid #333;">
            <p style="font-size: 12px; color: #888;">
              This is an automated confirmation. Please do not reply to this email.
              <br>
              Connection terminated safely.
            </p>
          </div>
        </div>
      </div>
    `;

    const textContent = `
Message Transmitted Successfully

Hello ${name},

Your message has been successfully transmitted through our secure communication channel.

Transmission Details:
- Subject: ${subject}
- Status: DELIVERED
- Encryption: ACTIVE
- Time: ${new Date().toLocaleString()}

I'll review your message and respond as soon as possible. Thank you for reaching out!

Best regards,
Sahil Ali
Full Stack Developer & Cybersecurity Expert
https://thestarsahil.me

---
This is an automated confirmation. Please do not reply to this email.
Connection terminated safely.
    `;

    await this.transporter.sendMail({
      from: config.emailFrom,
      to: email,
      subject: '✅ Message Transmitted Successfully - Sahil Ali',
      text: textContent,
      html: htmlContent,
    });

    logger.info(`Contact confirmation email sent to ${email}`);
  }

  async sendWelcomeEmail(email: string, name: string): Promise<void> {
    const htmlContent = `
      <div style="font-family: 'Fira Code', monospace; background-color: #0a0a0a; color: #00ffff; padding: 20px;">
        <div style="border: 1px solid #333; border-radius: 8px; padding: 20px; background-color: #121212;">
          <h2 style="color: #00ffff; margin-bottom: 20px;">🔐 Welcome to the Network</h2>
          
          <p>Hello <strong style="color: #6e0b75;">${name}</strong>,</p>
          
          <p>Welcome to my portfolio network! Your account has been successfully created.</p>
          
          <div style="background-color: #1a1a1a; border: 1px solid #333; border-radius: 4px; padding: 15px; margin: 20px 0;">
            <h3 style="color: #6e0b75; margin-bottom: 10px;">Access Granted:</h3>
            <p><strong>Email:</strong> ${email}</p>
            <p><strong>Status:</strong> <span style="color: #00ff00;">ACTIVE</span></p>
            <p><strong>Security Level:</strong> <span style="color: #00ff00;">ENCRYPTED</span></p>
          </div>
          
          <p>You now have access to exclusive content and features. Explore the dark web of technology!</p>
          
          <div style="margin-top: 30px; padding-top: 15px; border-top: 1px solid #333;">
            <p style="color: #6e0b75; font-weight: bold;">Sahil Ali</p>
            <p style="font-size: 14px; color: #888;">
              Full Stack Developer & Cybersecurity Expert<br>
              <a href="https://thestarsahil.me" style="color: #00ffff;">thestarsahil.me</a>
            </p>
          </div>
        </div>
      </div>
    `;

    await this.transporter.sendMail({
      from: config.emailFrom,
      to: email,
      subject: '🔐 Welcome to the Network - Sahil Ali',
      html: htmlContent,
    });

    logger.info(`Welcome email sent to ${email}`);
  }
}

export const emailService = new EmailService();
