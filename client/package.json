{"name": "@portfolio/client", "version": "2.0.0", "description": "Next.js frontend for <PERSON><PERSON>'s portfolio", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "analyze": "cross-env ANALYZE=true next build", "clean": "rm -rf .next out dist"}, "dependencies": {"@hookform/resolvers": "^3.3.2", "@portfolio/shared": "file:../shared", "@types/js-cookie": "^3.0.6", "@types/react-syntax-highlighter": "^15.5.11", "@types/three": "^0.159.0", "axios": "^1.6.2", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "date-fns": "^3.0.6", "embla-carousel-react": "^8.0.0", "framer-motion": "^10.16.16", "js-cookie": "^3.0.5", "lucide-react": "^0.294.0", "next": "^14.0.4", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.4.1", "react-intersection-observer": "^9.5.3", "react-markdown": "^9.0.1", "react-query": "^3.39.3", "react-syntax-highlighter": "^15.5.0", "rehype-highlight": "^7.0.0", "remark-gfm": "^4.0.0", "tailwind-merge": "^2.2.0", "tailwindcss-animate": "^1.0.7", "three": "^0.159.0", "zod": "^3.22.4"}, "devDependencies": {"@next/bundle-analyzer": "^14.0.4", "@testing-library/jest-dom": "^6.1.6", "@testing-library/react": "^14.1.2", "@types/node": "^20.10.5", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "autoprefixer": "^10.4.16", "cross-env": "^7.0.3", "eslint": "^8.56.0", "eslint-config-next": "^14.0.4", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8.4.32", "prettier": "^3.1.1", "prettier-plugin-tailwindcss": "^0.5.9", "tailwindcss": "^3.4.0", "typescript": "^5.3.3"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}