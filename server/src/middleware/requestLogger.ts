import { Request, Response, NextFunction } from 'express';
import { logger } from '@/utils/logger';

export const requestLogger = (req: Request, res: Response, next: NextFunction): void => {
  const start = Date.now();

  res.on('finish', () => {
    const duration = Date.now() - start;
    const { method, originalUrl, ip } = req;
    const { statusCode } = res;
    const userAgent = req.get('User-Agent') || '';

    logger.http(`${method} ${originalUrl}`, {
      statusCode,
      duration: `${duration}ms`,
      ip,
      userAgent,
    });
  });

  next();
};
