import { Router } from 'express';
import { HTTP_STATUS } from '@portfolio/shared';

const router = Router();

// Mock experience data
const mockExperience = [
  {
    id: '1',
    company: 'Microsoft',
    position: 'Microsoft Learn Student Ambassador',
    description: 'Promoted Microsoft technologies and organized tech events',
    startDate: '2023-01-01',
    endDate: '2024-01-01',
    location: 'Remote',
    type: 'volunteer',
    skills: ['Azure', 'Community Building', 'Public Speaking'],
    achievements: ['Fastest Coder Award', 'Organized 5+ tech events'],
  },
];

// Get all experience
router.get('/', (req, res) => {
  res.status(HTTP_STATUS.OK).json({
    success: true,
    data: mockExperience,
    timestamp: new Date().toISOString(),
  });
});

// Get experience by ID
router.get('/:id', (req, res) => {
  const experience = mockExperience.find(e => e.id === req.params.id);
  
  if (!experience) {
    return res.status(HTTP_STATUS.NOT_FOUND).json({
      success: false,
      error: 'Experience not found',
      timestamp: new Date().toISOString(),
    });
  }

  res.status(HTTP_STATUS.OK).json({
    success: true,
    data: experience,
    timestamp: new Date().toISOString(),
  });
});

export default router;
