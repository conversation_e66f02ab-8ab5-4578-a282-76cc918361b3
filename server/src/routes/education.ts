import { Router } from 'express';
import { HTTP_STATUS } from '@portfolio/shared';

const router = Router();

// Mock education data
const mockEducation = [
  {
    id: '1',
    institution: 'University Name',
    degree: 'Bachelor of Technology',
    field: 'Computer Science Engineering',
    startDate: '2020-08-01',
    endDate: '2024-06-01',
    description: 'Specialized in software development and cybersecurity',
    achievements: ['Dean\'s List', 'Competitive Programming Champion'],
  },
];

// Get all education
router.get('/', (req, res) => {
  res.status(HTTP_STATUS.OK).json({
    success: true,
    data: mockEducation,
    timestamp: new Date().toISOString(),
  });
});

// Get education by ID
router.get('/:id', (req, res) => {
  const education = mockEducation.find(e => e.id === req.params.id);
  
  if (!education) {
    return res.status(HTTP_STATUS.NOT_FOUND).json({
      success: false,
      error: 'Education record not found',
      timestamp: new Date().toISOString(),
    });
  }

  res.status(HTTP_STATUS.OK).json({
    success: true,
    data: education,
    timestamp: new Date().toISOString(),
  });
});

export default router;
