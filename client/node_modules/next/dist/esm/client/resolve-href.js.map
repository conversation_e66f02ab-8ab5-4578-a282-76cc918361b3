{"version": 3, "sources": ["../../src/client/resolve-href.ts"], "names": ["searchParamsToUrlQuery", "formatWithValidation", "omit", "normalizeRepeatedSlashes", "normalizePathTrailingSlash", "isLocalURL", "isDynamicRoute", "interpolateAs", "resolveHref", "router", "href", "resolveAs", "base", "urlAsString", "urlProtoMatch", "match", "urlAsStringNoProto", "slice", "length", "urlParts", "split", "console", "error", "pathname", "normalizedUrl", "URL", "startsWith", "<PERSON><PERSON><PERSON>", "_", "finalUrl", "interpolatedAs", "searchParams", "query", "result", "params", "hash", "resolvedHref", "origin"], "mappings": "AAEA,SAASA,sBAAsB,QAAQ,yCAAwC;AAC/E,SAASC,oBAAoB,QAAQ,wCAAuC;AAC5E,SAASC,IAAI,QAAQ,kCAAiC;AACtD,SAASC,wBAAwB,QAAQ,sBAAqB;AAC9D,SAASC,0BAA0B,QAAQ,6BAA4B;AACvE,SAASC,UAAU,QAAQ,0CAAyC;AACpE,SAASC,cAAc,QAAQ,6BAA4B;AAC3D,SAASC,aAAa,QAAQ,4CAA2C;AAgBzE,OAAO,SAASC,YACdC,MAAkB,EAClBC,IAAS,EACTC,SAAmB;IAEnB,4CAA4C;IAC5C,IAAIC;IACJ,IAAIC,cAAc,OAAOH,SAAS,WAAWA,OAAOT,qBAAqBS;IAEzE,6DAA6D;IAC7D,mDAAmD;IACnD,MAAMI,gBAAgBD,YAAYE,KAAK,CAAC;IACxC,MAAMC,qBAAqBF,gBACvBD,YAAYI,KAAK,CAACH,aAAa,CAAC,EAAE,CAACI,MAAM,IACzCL;IAEJ,MAAMM,WAAWH,mBAAmBI,KAAK,CAAC,KAAK;IAE/C,IAAI,AAACD,CAAAA,QAAQ,CAAC,EAAE,IAAI,EAAC,EAAGJ,KAAK,CAAC,cAAc;QAC1CM,QAAQC,KAAK,CACX,AAAC,mBAAgBT,cAAY,uCAAoCJ,OAAOc,QAAQ,GAAC;QAEnF,MAAMC,gBAAgBrB,yBAAyBa;QAC/CH,cAAc,AAACC,CAAAA,gBAAgBA,aAAa,CAAC,EAAE,GAAG,EAAC,IAAKU;IAC1D;IAEA,2DAA2D;IAC3D,IAAI,CAACnB,WAAWQ,cAAc;QAC5B,OAAQF,YAAY;YAACE;SAAY,GAAGA;IACtC;IAEA,IAAI;QACFD,OAAO,IAAIa,IACTZ,YAAYa,UAAU,CAAC,OAAOjB,OAAOkB,MAAM,GAAGlB,OAAOc,QAAQ,EAC7D;IAEJ,EAAE,OAAOK,GAAG;QACV,kDAAkD;QAClDhB,OAAO,IAAIa,IAAI,KAAK;IACtB;IAEA,IAAI;QACF,MAAMI,WAAW,IAAIJ,IAAIZ,aAAaD;QACtCiB,SAASN,QAAQ,GAAGnB,2BAA2ByB,SAASN,QAAQ;QAChE,IAAIO,iBAAiB;QAErB,IACExB,eAAeuB,SAASN,QAAQ,KAChCM,SAASE,YAAY,IACrBpB,WACA;YACA,MAAMqB,QAAQhC,uBAAuB6B,SAASE,YAAY;YAE1D,MAAM,EAAEE,MAAM,EAAEC,MAAM,EAAE,GAAG3B,cACzBsB,SAASN,QAAQ,EACjBM,SAASN,QAAQ,EACjBS;YAGF,IAAIC,QAAQ;gBACVH,iBAAiB7B,qBAAqB;oBACpCsB,UAAUU;oBACVE,MAAMN,SAASM,IAAI;oBACnBH,OAAO9B,KAAK8B,OAAOE;gBACrB;YACF;QACF;QAEA,oEAAoE;QACpE,MAAME,eACJP,SAASQ,MAAM,KAAKzB,KAAKyB,MAAM,GAC3BR,SAASnB,IAAI,CAACO,KAAK,CAACY,SAASQ,MAAM,CAACnB,MAAM,IAC1CW,SAASnB,IAAI;QAEnB,OAAOC,YACH;YAACyB;YAAcN,kBAAkBM;SAAa,GAC9CA;IACN,EAAE,OAAOR,GAAG;QACV,OAAOjB,YAAY;YAACE;SAAY,GAAGA;IACrC;AACF"}