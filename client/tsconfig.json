{"compilerOptions": {"target": "ES2020", "lib": ["dom", "dom.iterable", "ES6"], "allowJs": true, "skipLibCheck": true, "strict": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@/components/*": ["./src/components/*"], "@/pages/*": ["./src/pages/*"], "@/styles/*": ["./src/styles/*"], "@/hooks/*": ["./src/hooks/*"], "@/utils/*": ["./src/utils/*"], "@/types/*": ["./src/types/*"], "@/lib/*": ["./src/lib/*"], "@/config/*": ["./src/config/*"], "@/assets/*": ["./public/assets/*"]}}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules", ".next", "out", "dist"]}