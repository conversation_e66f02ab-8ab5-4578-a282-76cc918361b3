{"name": "sahil-portfolio", "version": "2.0.0", "description": "Modern full-stack portfolio application with client-server architecture", "private": true, "workspaces": ["client", "server", "shared"], "scripts": {"dev": "concurrently \"npm run dev:client\" \"npm run dev:server\"", "dev:client": "cd client && npm run dev", "dev:server": "cd server && npm run dev", "build": "npm run build:client && npm run build:server", "build:client": "cd client && npm run build", "build:server": "cd server && npm run build", "start": "concurrently \"npm run start:client\" \"npm run start:server\"", "start:client": "cd client && npm start", "start:server": "cd server && npm start", "test": "npm run test:client && npm run test:server", "test:client": "cd client && npm test", "test:server": "cd server && npm test", "lint": "npm run lint:client && npm run lint:server", "lint:client": "cd client && npm run lint", "lint:server": "cd server && npm run lint", "clean": "npm run clean:client && npm run clean:server", "clean:client": "cd client && rm -rf .next node_modules", "clean:server": "cd server && rm -rf dist node_modules", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f", "setup": "npm install && npm run setup:client && npm run setup:server", "setup:client": "cd client && npm install", "setup:server": "cd server && npm install"}, "devDependencies": {"concurrently": "^8.2.2", "@types/node": "^20.10.0", "typescript": "^5.3.0"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "repository": {"type": "git", "url": "https://github.com/thestarsahil/Portfolio.git"}, "keywords": ["portfolio", "fullstack", "nextjs", "nodejs", "express", "mongodb", "typescript", "cybersecurity", "dark-web-theme"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://thestarsahil.me"}, "license": "MIT"}