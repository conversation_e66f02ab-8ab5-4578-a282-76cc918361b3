{"version": 3, "sources": ["../../src/build/progress.ts"], "names": ["Log", "createSpinner", "divideSegments", "number", "segments", "result", "dividedNumber", "Math", "floor", "push", "createProgress", "total", "label", "Error", "currentSegmentTotal", "shift", "currentSegmentCount", "lastProgressOutput", "Date", "now", "curProgress", "progressSpinner", "spinner", "frames", "interval", "isFinished", "message", "setText", "stop", "event", "info", "process", "stdout", "isTTY"], "mappings": "AAAA,YAAYA,SAAS,sBAAqB;AAC1C,OAAOC,mBAAmB,YAAW;AAErC,SAASC,eAAeC,MAAc,EAAEC,QAAgB;IACtD,MAAMC,SAAS,EAAE;IACjB,MAAOF,SAAS,KAAKC,WAAW,EAAG;QACjC,MAAME,gBACJH,SAASC,WAAWD,SAASI,KAAKC,KAAK,CAACL,SAASC;QAEnDD,UAAUG;QACVF;QACAC,OAAOI,IAAI,CAACH;IACd;IACA,OAAOD;AACT;AAEA,OAAO,MAAMK,iBAAiB,CAACC,OAAeC;IAC5C,MAAMR,WAAWF,eAAeS,OAAO;IAEvC,IAAIA,UAAU,GAAG;QACf,MAAM,IAAIE,MAAM;IAClB;IACA,IAAIC,sBAAsBV,SAASW,KAAK;IACxC,IAAIC,sBAAsB;IAC1B,IAAIC,qBAAqBC,KAAKC,GAAG;IACjC,IAAIC,cAAc;IAClB,IAAIC,kBAAkBpB,cAAc,CAAC,EAAEW,MAAM,EAAE,EAAEQ,YAAY,CAAC,EAAET,MAAM,CAAC,CAAC,EAAE;QACxEW,SAAS;YACPC,QAAQ;gBACN;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACDC,UAAU;QACZ;IACF;IAEA,OAAO;QACLJ;QAEA,6BAA6B;QAC7B,oCAAoC;QACpC,eAAe;QACf,+BAA+B;QAC/B,IAAI,CAACC,iBAAiB;YACpBL;YAEA,IAAIA,wBAAwBF,qBAAqB;gBAC/CA,sBAAsBV,SAASW,KAAK;gBACpCC,sBAAsB;YACxB,OAAO,IAAIC,qBAAqB,QAAQC,KAAKC,GAAG,IAAI;gBAClD;YACF;YAEAF,qBAAqBC,KAAKC,GAAG;QAC/B;QAEA,MAAMM,aAAaL,gBAAgBT;QACnC,MAAMe,UAAU,CAAC,EAAEd,MAAM,EAAE,EAAEQ,YAAY,CAAC,EAAET,MAAM,CAAC,CAAC;QACpD,IAAIU,mBAAmB,CAACI,YAAY;YAClCJ,gBAAgBM,OAAO,CAACD;QAC1B,OAAO;YACLL,mCAAAA,gBAAiBO,IAAI;YACrB,IAAIH,YAAY;gBACdzB,IAAI6B,KAAK,CAACH;YACZ,OAAO;gBACL1B,IAAI8B,IAAI,CAAC,CAAC,EAAEJ,QAAQ,CAAC,EAAEK,QAAQC,MAAM,CAACC,KAAK,GAAG,OAAO,KAAK,CAAC;YAC7D;QACF;IACF;AACF,EAAC"}