import mongoose, { Document, Schema } from 'mongoose';
import { ProjectCategory, ProjectStatus, VALIDATION_RULES } from '@portfolio/shared';

export interface IProject extends Document {
  title: string;
  description: string;
  longDescription?: string;
  image: string;
  images?: string[];
  technologies: string[];
  githubUrl?: string;
  liveUrl?: string;
  featured: boolean;
  category: ProjectCategory;
  status: ProjectStatus;
  startDate: Date;
  endDate?: Date;
  createdAt: Date;
  updatedAt: Date;
}

const projectSchema = new Schema<IProject>(
  {
    title: {
      type: String,
      required: [true, 'Project title is required'],
      trim: true,
      maxlength: [VALIDATION_RULES.PROJECT_TITLE_MAX_LENGTH, `Title cannot be more than ${VALIDATION_RULES.PROJECT_TITLE_MAX_LENGTH} characters`],
    },
    description: {
      type: String,
      required: [true, 'Project description is required'],
      trim: true,
      maxlength: [VALIDATION_RULES.PROJECT_DESCRIPTION_MAX_LENGTH, `Description cannot be more than ${VALIDATION_RULES.PROJECT_DESCRIPTION_MAX_LENGTH} characters`],
    },
    longDescription: {
      type: String,
      trim: true,
      maxlength: [VALIDATION_RULES.PROJECT_LONG_DESCRIPTION_MAX_LENGTH, `Long description cannot be more than ${VALIDATION_RULES.PROJECT_LONG_DESCRIPTION_MAX_LENGTH} characters`],
    },
    image: {
      type: String,
      required: [true, 'Project image is required'],
    },
    images: [{
      type: String,
    }],
    technologies: [{
      type: String,
      required: true,
      trim: true,
    }],
    githubUrl: {
      type: String,
      validate: {
        validator: function(v: string) {
          if (!v) return true; // Optional field
          return /^https?:\/\/.+/.test(v);
        },
        message: 'Please provide a valid URL',
      },
    },
    liveUrl: {
      type: String,
      validate: {
        validator: function(v: string) {
          if (!v) return true; // Optional field
          return /^https?:\/\/.+/.test(v);
        },
        message: 'Please provide a valid URL',
      },
    },
    featured: {
      type: Boolean,
      default: false,
    },
    category: {
      type: String,
      enum: Object.values(ProjectCategory),
      required: [true, 'Project category is required'],
    },
    status: {
      type: String,
      enum: Object.values(ProjectStatus),
      default: ProjectStatus.PLANNING,
    },
    startDate: {
      type: Date,
      required: [true, 'Start date is required'],
    },
    endDate: {
      type: Date,
      validate: {
        validator: function(this: IProject, v: Date) {
          if (!v) return true; // Optional field
          return v > this.startDate;
        },
        message: 'End date must be after start date',
      },
    },
  },
  {
    timestamps: true,
    toJSON: {
      transform: function (doc, ret) {
        delete ret.__v;
        return ret;
      },
    },
  }
);

// Indexes for performance
projectSchema.index({ featured: 1 });
projectSchema.index({ category: 1 });
projectSchema.index({ status: 1 });
projectSchema.index({ startDate: -1 });
projectSchema.index({ createdAt: -1 });

// Text search index
projectSchema.index({
  title: 'text',
  description: 'text',
  longDescription: 'text',
  technologies: 'text',
});

// Static methods
projectSchema.statics.findFeatured = function () {
  return this.find({ featured: true }).sort({ startDate: -1 });
};

projectSchema.statics.findByCategory = function (category: ProjectCategory) {
  return this.find({ category }).sort({ startDate: -1 });
};

projectSchema.statics.findByStatus = function (status: ProjectStatus) {
  return this.find({ status }).sort({ startDate: -1 });
};

projectSchema.statics.findByTechnology = function (technology: string) {
  return this.find({ technologies: { $in: [technology] } }).sort({ startDate: -1 });
};

projectSchema.statics.search = function (query: string) {
  return this.find(
    { $text: { $search: query } },
    { score: { $meta: 'textScore' } }
  ).sort({ score: { $meta: 'textScore' } });
};

export const Project = mongoose.model<IProject>('Project', projectSchema);
