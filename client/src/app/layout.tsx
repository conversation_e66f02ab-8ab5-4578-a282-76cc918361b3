import type { Metada<PERSON> } from 'next';
import { Inter, Fira_Code, Orbitron } from 'next/font/google';
import './globals.css';
import { Providers } from '@/components/providers';
import { ThreeBackground } from '@/components/effects/ThreeBackground';
import { ScanlineOverlay } from '@/components/effects/ScanlineOverlay';
import { CRTFlicker } from '@/components/effects/CRTFlicker';
import { Analytics } from '@/components/Analytics';
import { Toaster } from 'react-hot-toast';

const inter = Inter({
  subsets: ['latin'],
  variable: '--font-inter',
  display: 'swap',
});

const firaCode = Fira_Code({
  subsets: ['latin'],
  variable: '--font-fira-code',
  display: 'swap',
});

const orbitron = Orbitron({
  subsets: ['latin'],
  variable: '--font-orbitron',
  display: 'swap',
});

export const metadata: Metadata = {
  title: {
    default: '<PERSON><PERSON> - Full Stack Developer & Cybersecurity Expert',
    template: '%s | <PERSON><PERSON>',
  },
  description: 'Full Stack Developer, Competitive Coder, and Cybersecurity Expert. Ex-Microsoft MLSA specializing in AI/ML, Web Development, and Embedded Systems.',
  keywords: [
    'Sahil Ali',
    'thestarsahil',
    'Full Stack Developer',
    'Cybersecurity',
    'AI/ML',
    'React',
    'Node.js',
    'Python',
    'C++',
    'Embedded Systems',
    'Microsoft MLSA',
    'Competitive Programming',
  ],
  authors: [{ name: 'Sahil Ali', url: 'https://thestarsahil.me' }],
  creator: 'Sahil Ali',
  publisher: 'Sahil Ali',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL(process.env.NEXT_PUBLIC_SITE_URL || 'https://thestarsahil.me'),
  alternates: {
    canonical: '/',
  },
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: '/',
    title: 'Sahil Ali - Full Stack Developer & Cybersecurity Expert',
    description: 'Full Stack Developer, Competitive Coder, and Cybersecurity Expert. Ex-Microsoft MLSA specializing in AI/ML, Web Development, and Embedded Systems.',
    siteName: 'Sahil Ali Portfolio',
    images: [
      {
        url: '/og-image.jpg',
        width: 1200,
        height: 630,
        alt: 'Sahil Ali - Portfolio',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Sahil Ali - Full Stack Developer & Cybersecurity Expert',
    description: 'Full Stack Developer, Competitive Coder, and Cybersecurity Expert.',
    images: ['/og-image.jpg'],
    creator: '@thestarsahil',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: process.env.NEXT_PUBLIC_GOOGLE_VERIFICATION,
  },
  category: 'technology',
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" className="dark">
      <head>
        <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
        <link rel="icon" type="image/png" href="/favicon.png" />
        <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
        <link rel="manifest" href="/manifest.json" />
        <meta name="theme-color" content="#00ffff" />
        <meta name="color-scheme" content="dark" />
        <meta name="viewport" content="width=device-width, initial-scale=1, viewport-fit=cover" />
      </head>
      <body className={`${inter.variable} ${firaCode.variable} ${orbitron.variable} antialiased`}>
        <Providers>
          {/* Background Effects */}
          <ThreeBackground />
          <ScanlineOverlay />
          <CRTFlicker />
          
          {/* Main Content */}
          <main className="relative z-10">
            {children}
          </main>
          
          {/* Toast Notifications */}
          <Toaster
            position="bottom-right"
            toastOptions={{
              duration: 4000,
              style: {
                background: '#1a1a1a',
                color: '#00ffff',
                border: '1px solid #333333',
                fontFamily: 'var(--font-fira-code)',
                fontSize: '14px',
              },
              success: {
                iconTheme: {
                  primary: '#00ff00',
                  secondary: '#1a1a1a',
                },
              },
              error: {
                iconTheme: {
                  primary: '#ff0000',
                  secondary: '#1a1a1a',
                },
              },
            }}
          />
          
          {/* Analytics */}
          <Analytics />
        </Providers>
      </body>
    </html>
  );
}
