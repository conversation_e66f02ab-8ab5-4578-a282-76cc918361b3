{"compilerOptions": {"target": "ES2020", "module": "commonjs", "lib": ["ES2020"], "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": true, "declarationMap": true, "sourceMap": true, "removeComments": false, "noImplicitAny": true, "noImplicitReturns": true, "noImplicitThis": true, "noUnusedLocals": true, "noUnusedParameters": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@/controllers/*": ["./src/controllers/*"], "@/models/*": ["./src/models/*"], "@/routes/*": ["./src/routes/*"], "@/middleware/*": ["./src/middleware/*"], "@/services/*": ["./src/services/*"], "@/utils/*": ["./src/utils/*"], "@/types/*": ["./src/types/*"], "@/config/*": ["./src/config/*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.spec.ts"]}