// Shared exports for the portfolio application

// Types
export * from './types';

// Utilities
export * from './utils';

// Constants
export const API_ENDPOINTS = {
  // Contact
  CONTACT: '/api/contact',
  CONTACT_LIST: '/api/contact/list',
  
  // Projects
  PROJECTS: '/api/projects',
  PROJECT_BY_ID: (id: string) => `/api/projects/${id}`,
  FEATURED_PROJECTS: '/api/projects/featured',
  
  // Skills
  SKILLS: '/api/skills',
  SKILL_BY_ID: (id: string) => `/api/skills/${id}`,
  SKILLS_BY_CATEGORY: (category: string) => `/api/skills/category/${category}`,
  
  // Experience
  EXPERIENCE: '/api/experience',
  EXPERIENCE_BY_ID: (id: string) => `/api/experience/${id}`,
  
  // Education
  EDUCATION: '/api/education',
  EDUCATION_BY_ID: (id: string) => `/api/education/${id}`,
  
  // Blog
  BLOG_POSTS: '/api/blog',
  BLOG_POST_BY_SLUG: (slug: string) => `/api/blog/${slug}`,
  BLOG_CATEGORIES: '/api/blog/categories',
  BLOG_TAGS: '/api/blog/tags',
  
  // Analytics
  ANALYTICS_TRACK: '/api/analytics/track',
  ANALYTICS_STATS: '/api/analytics/stats',
  
  // Auth
  AUTH_LOGIN: '/api/auth/login',
  AUTH_LOGOUT: '/api/auth/logout',
  AUTH_REFRESH: '/api/auth/refresh',
  AUTH_ME: '/api/auth/me',
  
  // Admin
  ADMIN_DASHBOARD: '/api/admin/dashboard',
  ADMIN_STATS: '/api/admin/stats',
  
  // File Upload
  UPLOAD_IMAGE: '/api/upload/image',
  UPLOAD_FILE: '/api/upload/file',
} as const;

export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  CONFLICT: 409,
  UNPROCESSABLE_ENTITY: 422,
  INTERNAL_SERVER_ERROR: 500,
  SERVICE_UNAVAILABLE: 503,
} as const;

export const ERROR_MESSAGES = {
  VALIDATION_ERROR: 'Validation error',
  UNAUTHORIZED: 'Unauthorized access',
  FORBIDDEN: 'Access forbidden',
  NOT_FOUND: 'Resource not found',
  INTERNAL_ERROR: 'Internal server error',
  INVALID_EMAIL: 'Invalid email format',
  INVALID_URL: 'Invalid URL format',
  REQUIRED_FIELD: 'This field is required',
  PASSWORD_TOO_WEAK: 'Password is too weak',
  EMAIL_ALREADY_EXISTS: 'Email already exists',
  INVALID_CREDENTIALS: 'Invalid credentials',
  TOKEN_EXPIRED: 'Token has expired',
  RATE_LIMIT_EXCEEDED: 'Rate limit exceeded',
} as const;

export const SUCCESS_MESSAGES = {
  CONTACT_SENT: 'Message sent successfully',
  PROJECT_CREATED: 'Project created successfully',
  PROJECT_UPDATED: 'Project updated successfully',
  PROJECT_DELETED: 'Project deleted successfully',
  SKILL_CREATED: 'Skill created successfully',
  SKILL_UPDATED: 'Skill updated successfully',
  SKILL_DELETED: 'Skill deleted successfully',
  LOGIN_SUCCESS: 'Login successful',
  LOGOUT_SUCCESS: 'Logout successful',
  PROFILE_UPDATED: 'Profile updated successfully',
} as const;

export const VALIDATION_RULES = {
  EMAIL_MAX_LENGTH: 255,
  NAME_MAX_LENGTH: 100,
  SUBJECT_MAX_LENGTH: 200,
  MESSAGE_MAX_LENGTH: 2000,
  PROJECT_TITLE_MAX_LENGTH: 100,
  PROJECT_DESCRIPTION_MAX_LENGTH: 500,
  PROJECT_LONG_DESCRIPTION_MAX_LENGTH: 5000,
  SKILL_NAME_MAX_LENGTH: 50,
  SKILL_DESCRIPTION_MAX_LENGTH: 500,
  PASSWORD_MIN_LENGTH: 8,
  PASSWORD_MAX_LENGTH: 128,
} as const;

export const PAGINATION_DEFAULTS = {
  PAGE: 1,
  LIMIT: 10,
  MAX_LIMIT: 100,
} as const;

export const CACHE_KEYS = {
  PROJECTS: 'projects',
  FEATURED_PROJECTS: 'featured_projects',
  SKILLS: 'skills',
  EXPERIENCE: 'experience',
  EDUCATION: 'education',
  BLOG_POSTS: 'blog_posts',
  ANALYTICS_STATS: 'analytics_stats',
} as const;

export const CACHE_TTL = {
  SHORT: 5 * 60, // 5 minutes
  MEDIUM: 30 * 60, // 30 minutes
  LONG: 60 * 60, // 1 hour
  VERY_LONG: 24 * 60 * 60, // 24 hours
} as const;
