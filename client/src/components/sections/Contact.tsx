'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Mail, MapPin, User, Key, Send, Shield } from 'lucide-react';
import toast from 'react-hot-toast';

const contactSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters'),
  email: z.string().email('Invalid email address'),
  subject: z.string().min(5, 'Subject must be at least 5 characters'),
  message: z.string().min(10, 'Message must be at least 10 characters'),
});

type ContactFormData = z.infer<typeof contactSchema>;

export function Contact() {
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  const {
    register,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<ContactFormData>({
    resolver: zodResolver(contactSchema),
  });

  const onSubmit = async (data: ContactFormData) => {
    setIsSubmitting(true);
    
    try {
      // Simulate encryption animation
      toast.loading('Encrypting message...', { id: 'contact' });
      
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      toast.loading('Transmitting...', { id: 'contact' });
      
      // TODO: Replace with actual API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      toast.success('Message transmitted securely!', { id: 'contact' });
      reset();
    } catch (error) {
      toast.error('Transmission failed. Please try again.', { id: 'contact' });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <section id="contact" className="section-padding bg-darkweb-surface/30">
      <div className="container-custom">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl md:text-4xl font-bold text-darkweb-text mb-4 flex items-center justify-center gap-4">
            <img src="/assets/tor-logo.svg" alt="Tor Network" className="w-8 h-8" />
            <span className="text-darkweb-accent glitch-text" data-text="Contact me">
              Contact me
            </span>
          </h2>
          <div className="w-24 h-1 bg-darkweb-accent mx-auto mb-8"></div>
          
          <div className="bg-darkweb-card border border-darkweb-border rounded-lg p-6 max-w-md mx-auto">
            <div className="glitch-text text-darkweb-accent font-bold mb-2" data-text="SECURE COMMUNICATION CHANNEL">
              SECURE COMMUNICATION CHANNEL
            </div>
            <div className="text-darkweb-muted text-sm">Encryption: PGP/GPG | Protocol: TOR</div>
          </div>
        </motion.div>

        <div className="grid lg:grid-cols-2 gap-12">
          {/* Contact Info */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="terminal-window"
          >
            <div className="terminal-header">
              <div className="terminal-buttons">
                <span className="terminal-button close"></span>
                <span className="terminal-button minimize"></span>
                <span className="terminal-button maximize"></span>
              </div>
              <div className="terminal-title glitch-text" data-text="kali@sahil: ~/contact">
                kali@sahil: ~/contact
              </div>
            </div>
            <div className="terminal-body">
              <div className="terminal-line">cat contact_info.txt</div>
              <div className="space-y-6">
                <div className="flex items-center gap-4">
                  <User className="w-5 h-5 text-darkweb-accent" />
                  <div>
                    <div className="text-darkweb-muted text-sm">Identity</div>
                    <div className="text-darkweb-text font-bold glitch-text" data-text="Sahil Ali">
                      Sahil Ali
                    </div>
                  </div>
                </div>
                
                <div className="flex items-center gap-4">
                  <MapPin className="w-5 h-5 text-darkweb-accent" />
                  <div>
                    <div className="text-darkweb-muted text-sm">Location</div>
                    <div className="text-darkweb-text">Anonymous</div>
                  </div>
                </div>
                
                <div className="flex items-center gap-4">
                  <Mail className="w-5 h-5 text-darkweb-accent" />
                  <div>
                    <div className="text-darkweb-muted text-sm">Email</div>
                    <div className="text-darkweb-text"><EMAIL></div>
                  </div>
                </div>
                
                <div className="flex items-center gap-4">
                  <Key className="w-5 h-5 text-darkweb-accent" />
                  <div>
                    <div className="text-darkweb-muted text-sm">PGP Key</div>
                    <div className="text-darkweb-text font-mono text-sm">4B1D C371 57E3 2F8A...</div>
                  </div>
                </div>
              </div>
              
              <div className="terminal-line mt-6">./verify_identity.sh</div>
              <div className="flex items-center gap-2 text-darkweb-green mt-2">
                <span>Identity Verified</span>
                <Shield className="w-4 h-4" />
              </div>
            </div>
          </motion.div>

          {/* Contact Form */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="terminal-window"
          >
            <div className="terminal-header">
              <div className="terminal-buttons">
                <span className="terminal-button close"></span>
                <span className="terminal-button minimize"></span>
                <span className="terminal-button maximize"></span>
              </div>
              <div className="terminal-title glitch-text" data-text="kali@sahil: ~/message">
                kali@sahil: ~/message
              </div>
            </div>
            <div className="terminal-body">
              <div className="terminal-line">./send_encrypted_message.sh</div>
              
              <form onSubmit={handleSubmit(onSubmit)} className="space-y-6 mt-4">
                <div className="grid md:grid-cols-2 gap-4">
                  <div>
                    <input
                      {...register('name')}
                      type="text"
                      placeholder="Identity"
                      className="input-terminal"
                      disabled={isSubmitting}
                    />
                    {errors.name && (
                      <p className="text-darkweb-red text-xs mt-1">{errors.name.message}</p>
                    )}
                  </div>
                  
                  <div>
                    <input
                      {...register('email')}
                      type="email"
                      placeholder="Secure Email"
                      className="input-terminal"
                      disabled={isSubmitting}
                    />
                    {errors.email && (
                      <p className="text-darkweb-red text-xs mt-1">{errors.email.message}</p>
                    )}
                  </div>
                </div>
                
                <div>
                  <input
                    {...register('subject')}
                    type="text"
                    placeholder="Message Subject"
                    className="input-terminal"
                    disabled={isSubmitting}
                  />
                  {errors.subject && (
                    <p className="text-darkweb-red text-xs mt-1">{errors.subject.message}</p>
                  )}
                </div>
                
                <div>
                  <textarea
                    {...register('message')}
                    rows={6}
                    placeholder="Encrypted Message..."
                    className="input-terminal resize-none"
                    disabled={isSubmitting}
                  />
                  {errors.message && (
                    <p className="text-darkweb-red text-xs mt-1">{errors.message.message}</p>
                  )}
                </div>
                
                <div className="flex items-center justify-between text-sm">
                  <div className="flex items-center gap-2 text-darkweb-green">
                    <Shield className="w-4 h-4" />
                    <span>End-to-End Encryption Active</span>
                  </div>
                </div>
                
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="btn-primary w-full flex items-center justify-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isSubmitting ? (
                    <div className="loading-spinner" />
                  ) : (
                    <Send className="w-4 h-4" />
                  )}
                  {isSubmitting ? 'Transmitting...' : 'Transmit Secure Message'}
                </button>
              </form>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
}
