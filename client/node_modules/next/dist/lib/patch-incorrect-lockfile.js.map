{"version": 3, "sources": ["../../src/lib/patch-incorrect-lockfile.ts"], "names": ["patchIncorrectLockfile", "registry", "fetchPkgInfo", "pkg", "getRegistry", "res", "fetch", "ok", "Error", "status", "data", "json", "versionData", "versions", "nextPkgJson", "version", "os", "cpu", "engines", "tarball", "dist", "integrity", "dir", "process", "env", "NEXT_IGNORE_INCORRECT_LOCKFILE", "lockfilePath", "findUp", "cwd", "content", "promises", "readFile", "endingNewline", "endsWith", "lockfileParsed", "JSON", "parse", "lockfileVersion", "parseInt", "expectedSwcPkgs", "Object", "keys", "filter", "startsWith", "patchDependency", "pkgData", "dependencies", "resolved", "optional", "patchPackage", "packages", "supportedVersions", "includes", "shouldPatchDependencies", "shouldPatchPackages", "missingSwcPkgs", "pkgPrefix", "substring", "length", "push", "Log", "warn", "isCI", "pkgsData", "Promise", "all", "map", "i", "writeFile", "stringify", "err", "error", "console"], "mappings": ";;;;+BAsCsBA;;;eAAAA;;;oBAtCG;6DACJ;+DACF;oEAEK;wBAEH;6BACO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE5B,IAAIC;AAEJ,eAAeC,aAAaC,GAAW;IACrC,IAAI,CAACF,UAAUA,WAAWG,IAAAA,wBAAW;IACrC,MAAMC,MAAM,MAAMC,MAAM,CAAC,EAAEL,SAAS,EAAEE,IAAI,CAAC;IAE3C,IAAI,CAACE,IAAIE,EAAE,EAAE;QACX,MAAM,IAAIC,MACR,CAAC,kCAAkC,EAAEL,IAAI,aAAa,EAAEE,IAAII,MAAM,CAAC,CAAC;IAExE;IACA,MAAMC,OAAO,MAAML,IAAIM,IAAI;IAC3B,MAAMC,cAAcF,KAAKG,QAAQ,CAACC,oBAAW,CAACC,OAAO,CAAC;IAEtD,OAAO;QACLC,IAAIJ,YAAYI,EAAE;QAClBC,KAAKL,YAAYK,GAAG;QACpBC,SAASN,YAAYM,OAAO;QAC5BC,SAASP,YAAYQ,IAAI,CAACD,OAAO;QACjCE,WAAWT,YAAYQ,IAAI,CAACC,SAAS;IACvC;AACF;AAQO,eAAerB,uBAAuBsB,GAAW;IACtD,IAAIC,QAAQC,GAAG,CAACC,8BAA8B,EAAE;QAC9C;IACF;IACA,MAAMC,eAAe,MAAMC,IAAAA,eAAM,EAAC,qBAAqB;QAAEC,KAAKN;IAAI;IAElE,IAAI,CAACI,cAAc;QACjB,oDAAoD;QACpD;IACF;IACA,MAAMG,UAAU,MAAMC,YAAQ,CAACC,QAAQ,CAACL,cAAc;IACtD,+BAA+B;IAC/B,MAAMM,gBAAgBH,QAAQI,QAAQ,CAAC,UACnC,SACAJ,QAAQI,QAAQ,CAAC,QACjB,OACA;IAEJ,MAAMC,iBAAiBC,KAAKC,KAAK,CAACP;IAClC,MAAMQ,kBAAkBC,SAASJ,kCAAAA,eAAgBG,eAAe,EAAE;IAClE,MAAME,kBAAkBC,OAAOC,IAAI,CACjC3B,oBAAW,CAAC,uBAAuB,IAAI,CAAC,GACxC4B,MAAM,CAAC,CAACvC,MAAQA,IAAIwC,UAAU,CAAC;IAEjC,MAAMC,kBAAkB,CACtBzC,KACA0C;QAEAX,eAAeY,YAAY,CAAC3C,IAAI,GAAG;YACjCY,SAASD,oBAAW,CAACC,OAAO;YAC5BgC,UAAUF,QAAQ1B,OAAO;YACzBE,WAAWwB,QAAQxB,SAAS;YAC5B2B,UAAU;QACZ;IACF;IAEA,MAAMC,eAAe,CACnB9C,KACA0C;QAEAX,eAAegB,QAAQ,CAAC/C,IAAI,GAAG;YAC7BY,SAASD,oBAAW,CAACC,OAAO;YAC5BgC,UAAUF,QAAQ1B,OAAO;YACzBE,WAAWwB,QAAQxB,SAAS;YAC5BJ,KAAK4B,QAAQ5B,GAAG;YAChB+B,UAAU;YACVhC,IAAI6B,QAAQ7B,EAAE;YACdE,SAAS2B,QAAQ3B,OAAO;QAC1B;IACF;IAEA,IAAI;QACF,MAAMiC,oBAAoB;YAAC;YAAG;YAAG;SAAE;QAEnC,IAAI,CAACA,kBAAkBC,QAAQ,CAACf,kBAAkB;YAChD,8BAA8B;YAC9B;QACF;QACA,4BAA4B;QAC5B,oCAAoC;QACpC,wBAAwB;QACxB,MAAMgB,0BACJhB,oBAAoB,KAAKA,oBAAoB;QAC/C,MAAMiB,sBAAsBjB,oBAAoB,KAAKA,oBAAoB;QAEzE,IACE,AAACgB,2BAA2B,CAACnB,eAAeY,YAAY,IACvDQ,uBAAuB,CAACpB,eAAegB,QAAQ,EAChD;YACA,2BAA2B;YAC3B;QACF;QACA,MAAMK,iBAAiB,EAAE;QACzB,IAAIC;QAEJ,IAAIF,qBAAqB;YACvBE,YAAY;YACZ,KAAK,MAAMrD,OAAOqC,OAAOC,IAAI,CAACP,eAAegB,QAAQ,EAAG;gBACtD,IAAI/C,IAAI8B,QAAQ,CAAC,sBAAsB;oBACrCuB,YAAYrD,IAAIsD,SAAS,CAAC,GAAGtD,IAAIuD,MAAM,GAAG;gBAC5C;YACF;YAEA,IAAI,CAACF,WAAW;gBACd,4CAA4C;gBAC5C;YACF;QACF;QAEA,KAAK,MAAMrD,OAAOoC,gBAAiB;YACjC,IACE,AAACc,2BAA2B,CAACnB,eAAeY,YAAY,CAAC3C,IAAI,IAC5DmD,uBAAuB,CAACpB,eAAegB,QAAQ,CAAC,CAAC,EAAEM,UAAU,EAAErD,IAAI,CAAC,CAAC,EACtE;gBACAoD,eAAeI,IAAI,CAACxD;YACtB;QACF;QACA,IAAIoD,eAAeG,MAAM,KAAK,GAAG;YAC/B;QACF;QACAE,KAAIC,IAAI,CACN,CAAC,wCAAwC,CAAC,EAC1CC,YAAI,GAAG,4CAA4C;QAGrD,IAAIA,YAAI,EAAE;YACR,8DAA8D;YAC9D;QACF;QACA,MAAMC,WAAW,MAAMC,QAAQC,GAAG,CAChCV,eAAeW,GAAG,CAAC,CAAC/D,MAAQD,aAAaC;QAG3C,IAAK,IAAIgE,IAAI,GAAGA,IAAIJ,SAASL,MAAM,EAAES,IAAK;YACxC,MAAMhE,MAAMoD,cAAc,CAACY,EAAE;YAC7B,MAAMtB,UAAUkB,QAAQ,CAACI,EAAE;YAE3B,IAAId,yBAAyB;gBAC3BT,gBAAgBzC,KAAK0C;YACvB;YACA,IAAIS,qBAAqB;gBACvBL,aAAa,CAAC,EAAEO,UAAU,EAAErD,IAAI,CAAC,EAAE0C;YACrC;QACF;QAEA,MAAMf,YAAQ,CAACsC,SAAS,CACtB1C,cACAS,KAAKkC,SAAS,CAACnC,gBAAgB,MAAM,KAAKF;QAE5C4B,KAAIC,IAAI,CACN;IAEJ,EAAE,OAAOS,KAAK;QACZV,KAAIW,KAAK,CACP,CAAC,yFAAyF,CAAC;QAE7FC,QAAQD,KAAK,CAACD;IAChB;AACF"}