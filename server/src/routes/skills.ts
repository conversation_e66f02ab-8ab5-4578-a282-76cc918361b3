import { Router } from 'express';
import { HTTP_STATUS } from '@portfolio/shared';

const router = Router();

// Mock skills data
const mockSkills = [
  {
    id: '1',
    name: 'Prompt Engineering',
    category: 'ai-ml',
    proficiency: 62,
    icon: 'terminal',
    description: 'Advanced prompt engineering for AI models',
  },
  {
    id: '2',
    name: 'AI Model Development',
    category: 'ai-ml',
    proficiency: 31,
    icon: 'bot',
    description: 'Building and training AI models',
  },
  {
    id: '3',
    name: 'C++ Programming',
    category: 'programming-languages',
    proficiency: 65,
    icon: 'code',
    description: 'Embedded systems and performance-critical applications',
  },
  {
    id: '4',
    name: 'Server & Database Management',
    category: 'databases',
    proficiency: 62,
    icon: 'database',
    description: 'Database design and server administration',
  },
  {
    id: '5',
    name: 'Data Structures & Algorithms',
    category: 'programming-languages',
    proficiency: 53,
    icon: 'cpu',
    description: 'Competitive programming and algorithm optimization',
  },
];

// Get all skills
router.get('/', (req, res) => {
  const { category } = req.query;
  
  let filteredSkills = [...mockSkills];
  
  if (category) {
    filteredSkills = filteredSkills.filter(s => s.category === category);
  }

  res.status(HTTP_STATUS.OK).json({
    success: true,
    data: filteredSkills,
    timestamp: new Date().toISOString(),
  });
});

// Get skills by category
router.get('/category/:category', (req, res) => {
  const { category } = req.params;
  const categorySkills = mockSkills.filter(s => s.category === category);

  res.status(HTTP_STATUS.OK).json({
    success: true,
    data: categorySkills,
    timestamp: new Date().toISOString(),
  });
});

// Get skill by ID
router.get('/:id', (req, res) => {
  const skill = mockSkills.find(s => s.id === req.params.id);
  
  if (!skill) {
    return res.status(HTTP_STATUS.NOT_FOUND).json({
      success: false,
      error: 'Skill not found',
      timestamp: new Date().toISOString(),
    });
  }

  res.status(HTTP_STATUS.OK).json({
    success: true,
    data: skill,
    timestamp: new Date().toISOString(),
  });
});

export default router;
