{"version": 3, "sources": ["../../src/lib/recursive-readdir.ts"], "names": ["fs", "path", "recursiveReadDir", "rootDirectory", "options", "pathnameFilter", "ignoreFilter", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sortPathnames", "relativePathnames", "pathnames", "coerce", "pathname", "replace", "directories", "length", "results", "Promise", "all", "map", "directory", "result", "links", "dir", "readdir", "withFileTypes", "file", "name", "absolutePathname", "join", "isDirectory", "push", "isSymbolicLink", "err", "code", "resolved", "stat", "i", "stats", "sort"], "mappings": "AAAA,OAAOA,QAAQ,cAAa;AAC5B,OAAOC,UAAU,OAAM;AAuCvB;;;;;;CAMC,GACD,OAAO,eAAeC,iBACpBC,aAAqB,EACrBC,UAAmC,CAAC,CAAC;IAErC,oBAAoB;IACpB,MAAM,EACJC,cAAc,EACdC,YAAY,EACZC,gBAAgB,EAChBC,gBAAgB,IAAI,EACpBC,oBAAoB,IAAI,EACzB,GAAGL;IAEJ,mCAAmC;IACnC,MAAMM,YAAsB,EAAE;IAE9B;;GAEC,GACD,MAAMC,SAASF,oBACX,CAACG,WAAqBA,SAASC,OAAO,CAACV,eAAe,MACtD,CAACS,WAAqBA;IAE1B,oCAAoC;IACpC,IAAIE,cAAwB;QAACX;KAAc;IAE3C,MAAOW,YAAYC,MAAM,GAAG,EAAG;QAC7B,yDAAyD;QACzD,MAAMC,UAAU,MAAMC,QAAQC,GAAG,CAC/BJ,YAAYK,GAAG,CAAC,OAAOC;YACrB,MAAMC,SAAiB;gBAAEP,aAAa,EAAE;gBAAEJ,WAAW,EAAE;gBAAEY,OAAO,EAAE;YAAC;YAEnE,IAAI;gBACF,MAAMC,MAAM,MAAMvB,GAAGwB,OAAO,CAACJ,WAAW;oBAAEK,eAAe;gBAAK;gBAC9D,KAAK,MAAMC,QAAQH,IAAK;oBACtB,+DAA+D;oBAC/D,IAAIhB,oBAAoBA,iBAAiBmB,KAAKC,IAAI,GAAG;wBACnD;oBACF;oBAEA,oBAAoB;oBACpB,MAAMC,mBAAmB3B,KAAK4B,IAAI,CAACT,WAAWM,KAAKC,IAAI;oBAEvD,+DAA+D;oBAC/D,IAAIrB,gBAAgBA,aAAasB,mBAAmB;wBAClD;oBACF;oBAEA,sEAAsE;oBACtE,sCAAsC;oBACtC,IAAIF,KAAKI,WAAW,IAAI;wBACtBT,OAAOP,WAAW,CAACiB,IAAI,CAACH;oBAC1B,OAAO,IAAIF,KAAKM,cAAc,IAAI;wBAChCX,OAAOC,KAAK,CAACS,IAAI,CAACH;oBACpB,OAAO,IAAI,CAACvB,kBAAkBA,eAAeuB,mBAAmB;wBAC9DP,OAAOX,SAAS,CAACqB,IAAI,CAACpB,OAAOiB;oBAC/B;gBACF;YACF,EAAE,OAAOK,KAAU;gBACjB,qEAAqE;gBACrE,sDAAsD;gBACtD,uCAAuC;gBACvC,IAAIA,IAAIC,IAAI,KAAK,YAAYd,cAAcjB,eAAe,MAAM8B;gBAEhE,yDAAyD;gBACzD,OAAO;YACT;YAEA,OAAOZ;QACT;QAGF,sEAAsE;QACtE,eAAe;QACfP,cAAc,EAAE;QAEhB,sEAAsE;QACtE,MAAMQ,QAAQ,EAAE;QAEhB,wCAAwC;QACxC,KAAK,MAAMD,UAAUL,QAAS;YAC5B,8CAA8C;YAC9C,IAAI,CAACK,QAAQ;YAEb,0DAA0D;YAC1DP,YAAYiB,IAAI,IAAIV,OAAOP,WAAW;YAEtC,mEAAmE;YACnEQ,MAAMS,IAAI,IAAIV,OAAOC,KAAK;YAE1B,mDAAmD;YACnDZ,UAAUqB,IAAI,IAAIV,OAAOX,SAAS;QACpC;QAEA,kDAAkD;QAClD,IAAIY,MAAMP,MAAM,GAAG,GAAG;YACpB,MAAMoB,WAAW,MAAMlB,QAAQC,GAAG,CAChCI,MAAMH,GAAG,CAAC,OAAOS;gBACf,IAAI;oBACF,OAAO,MAAM5B,GAAGoC,IAAI,CAACR;gBACvB,EAAE,OAAOK,KAAU;oBACjB,gEAAgE;oBAChE,sDAAsD;oBACtD,IAAIA,IAAIC,IAAI,KAAK,UAAU,MAAMD;oBAEjC,yDAAyD;oBACzD,OAAO;gBACT;YACF;YAGF,IAAK,IAAII,IAAI,GAAGA,IAAIf,MAAMP,MAAM,EAAEsB,IAAK;gBACrC,MAAMC,QAAQH,QAAQ,CAACE,EAAE;gBAEzB,yCAAyC;gBACzC,IAAI,CAACC,OAAO;gBAEZ,kEAAkE;gBAClE,8CAA8C;gBAC9C,MAAMV,mBAAmBN,KAAK,CAACe,EAAE;gBAEjC,IAAIC,MAAMR,WAAW,IAAI;oBACvBhB,YAAYiB,IAAI,CAACH;gBACnB,OAAO,IAAI,CAACvB,kBAAkBA,eAAeuB,mBAAmB;oBAC9DlB,UAAUqB,IAAI,CAACpB,OAAOiB;gBACxB;YACF;QACF;IACF;IAEA,4CAA4C;IAC5C,IAAIpB,eAAe;QACjBE,UAAU6B,IAAI;IAChB;IAEA,OAAO7B;AACT"}