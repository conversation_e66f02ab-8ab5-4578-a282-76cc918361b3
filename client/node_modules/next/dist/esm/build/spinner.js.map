{"version": 3, "sources": ["../../src/build/spinner.ts"], "names": ["ora", "Log", "dots<PERSON>pinner", "frames", "interval", "createSpinner", "text", "options", "logFn", "console", "log", "spinner", "prefixText", "prefixes", "info", "process", "stdout", "isTTY", "undefined", "stream", "start", "origLog", "origWarn", "warn", "origError", "error", "origStop", "stop", "bind", "origStopAndPersist", "stopAndPersist", "logHandle", "method", "args", "resetLog", "setText", "newText", "suffixText", "event"], "mappings": "AAAA,OAAOA,SAAS,yBAAwB;AACxC,YAAYC,SAAS,eAAc;AAEnC,MAAMC,cAAc;IAClBC,QAAQ;QAAC;QAAK;QAAM;KAAM;IAC1BC,UAAU;AACZ;AAEA,eAAe,SAASC,cACtBC,IAAY,EACZC,UAAuB,CAAC,CAAC,EACzBC,QAAkCC,QAAQC,GAAG;IAE7C,IAAIC;IAEJ,IAAIC,aAAa,CAAC,CAAC,EAAEX,IAAIY,QAAQ,CAACC,IAAI,CAAC,CAAC,EAAER,KAAK,CAAC,CAAC;IAEjD,IAAIS,QAAQC,MAAM,CAACC,KAAK,EAAE;QACxBN,UAAUX,IAAI;YACZM,MAAMY;YACNN;YACAD,SAAST;YACTiB,QAAQJ,QAAQC,MAAM;YACtB,GAAGT,OAAO;QACZ,GAAGa,KAAK;QAER,2DAA2D;QAC3D,+DAA+D;QAC/D,MAAMC,UAAUZ,QAAQC,GAAG;QAC3B,MAAMY,WAAWb,QAAQc,IAAI;QAC7B,MAAMC,YAAYf,QAAQgB,KAAK;QAC/B,MAAMC,WAAWf,QAAQgB,IAAI,CAACC,IAAI,CAACjB;QACnC,MAAMkB,qBAAqBlB,QAAQmB,cAAc,CAACF,IAAI,CAACjB;QAEvD,MAAMoB,YAAY,CAACC,QAAaC;YAC9BP;YACAM,UAAUC;YACVtB,QAASS,KAAK;QAChB;QAEAX,QAAQC,GAAG,GAAG,CAAC,GAAGuB,OAAcF,UAAUV,SAASY;QACnDxB,QAAQc,IAAI,GAAG,CAAC,GAAGU,OAAcF,UAAUT,UAAUW;QACrDxB,QAAQgB,KAAK,GAAG,CAAC,GAAGQ,OAAcF,UAAUP,WAAWS;QAEvD,MAAMC,WAAW;YACfzB,QAAQC,GAAG,GAAGW;YACdZ,QAAQc,IAAI,GAAGD;YACfb,QAAQgB,KAAK,GAAGD;QAClB;QACAb,QAAQwB,OAAO,GAAG,CAACC;YACjB9B,OAAO8B;YACPxB,aAAa,CAAC,CAAC,EAAEX,IAAIY,QAAQ,CAACC,IAAI,CAAC,CAAC,EAAEsB,QAAQ,CAAC,CAAC;YAChDzB,QAASC,UAAU,GAAGA;YACtB,OAAOD;QACT;QACAA,QAAQgB,IAAI,GAAG;YACbD;YACAQ;YACA,OAAOvB;QACT;QACAA,QAAQmB,cAAc,GAAG;YACvB,uEAAuE;YACvE,MAAMO,aAAa,CAAC,GAAG,EAAEpC,IAAIY,QAAQ,CAACyB,KAAK,CAAC,CAAC,EAAEhC,KAAK,CAAC,CAAC;YACtD,IAAIK,SAAS;gBACXA,QAAQL,IAAI,GAAG+B;YACjB,OAAO;gBACL7B,MAAM6B;YACR;YACAR;YACAK;YACA,OAAOvB;QACT;IACF,OAAO,IAAIC,cAAcN,MAAM;QAC7BE,MAAMI,aAAaA,aAAa,QAAQN;IAC1C;IAEA,OAAOK;AACT"}