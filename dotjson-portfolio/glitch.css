/* Dark Web Glitch Effects */

/* Glitch text effect */
.glitch-text {
  position: relative;
  animation: glitch-skew 1s infinite linear alternate-reverse;
}

.glitch-text::before,
.glitch-text::after {
  content: attr(data-text);
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.glitch-text::before {
  left: 2px;
  text-shadow: -2px 0 var(--darkweb-purple);
  clip: rect(44px, 450px, 56px, 0);
  animation: glitch-anim 5s infinite linear alternate-reverse;
}

.glitch-text::after {
  left: -2px;
  text-shadow: -2px 0 var(--darkweb-cyan);
  clip: rect(44px, 450px, 56px, 0);
  animation: glitch-anim2 5s infinite linear alternate-reverse;
}

@keyframes glitch-anim {
  0% {
    clip: rect(31px, 9999px, 94px, 0);
    transform: skew(0.85deg);
  }
  5% {
    clip: rect(70px, 9999px, 71px, 0);
    transform: skew(0.17deg);
  }
  10% {
    clip: rect(75px, 9999px, 92px, 0);
    transform: skew(0.4deg);
  }
  15% {
    clip: rect(12px, 9999px, 23px, 0);
    transform: skew(0.01deg);
  }
  20% {
    clip: rect(18px, 9999px, 13px, 0);
    transform: skew(0.65deg);
  }
  25% {
    clip: rect(38px, 9999px, 43px, 0);
    transform: skew(0.29deg);
  }
  30% {
    clip: rect(81px, 9999px, 4px, 0);
    transform: skew(0.02deg);
  }
  35% {
    clip: rect(86px, 9999px, 25px, 0);
    transform: skew(0.08deg);
  }
  40% {
    clip: rect(93px, 9999px, 46px, 0);
    transform: skew(0.71deg);
  }
  45% {
    clip: rect(100px, 9999px, 85px, 0);
    transform: skew(0.65deg);
  }
  50% {
    clip: rect(25px, 9999px, 100px, 0);
    transform: skew(0.89deg);
  }
  55% {
    clip: rect(4px, 9999px, 69px, 0);
    transform: skew(0.01deg);
  }
  60% {
    clip: rect(76px, 9999px, 4px, 0);
    transform: skew(0.64deg);
  }
  65% {
    clip: rect(30px, 9999px, 49px, 0);
    transform: skew(0.52deg);
  }
  70% {
    clip: rect(33px, 9999px, 31px, 0);
    transform: skew(0.81deg);
  }
  75% {
    clip: rect(95px, 9999px, 39px, 0);
    transform: skew(0.34deg);
  }
  80% {
    clip: rect(26px, 9999px, 59px, 0);
    transform: skew(0.63deg);
  }
  85% {
    clip: rect(76px, 9999px, 90px, 0);
    transform: skew(0.02deg);
  }
  90% {
    clip: rect(38px, 9999px, 65px, 0);
    transform: skew(0.44deg);
  }
  95% {
    clip: rect(82px, 9999px, 41px, 0);
    transform: skew(0.07deg);
  }
  100% {
    clip: rect(98px, 9999px, 71px, 0);
    transform: skew(0.01deg);
  }
}

@keyframes glitch-anim2 {
  0% {
    clip: rect(65px, 9999px, 65px, 0);
    transform: skew(0.19deg);
  }
  5% {
    clip: rect(31px, 9999px, 21px, 0);
    transform: skew(0.66deg);
  }
  10% {
    clip: rect(33px, 9999px, 72px, 0);
    transform: skew(0.89deg);
  }
  15% {
    clip: rect(2px, 9999px, 66px, 0);
    transform: skew(0.36deg);
  }
  20% {
    clip: rect(60px, 9999px, 89px, 0);
    transform: skew(0.4deg);
  }
  25% {
    clip: rect(14px, 9999px, 8px, 0);
    transform: skew(0.58deg);
  }
  30% {
    clip: rect(73px, 9999px, 82px, 0);
    transform: skew(0.75deg);
  }
  35% {
    clip: rect(71px, 9999px, 33px, 0);
    transform: skew(0.26deg);
  }
  40% {
    clip: rect(64px, 9999px, 35px, 0);
    transform: skew(0.52deg);
  }
  45% {
    clip: rect(84px, 9999px, 74px, 0);
    transform: skew(0.61deg);
  }
  50% {
    clip: rect(100px, 9999px, 21px, 0);
    transform: skew(0.01deg);
  }
  55% {
    clip: rect(77px, 9999px, 97px, 0);
    transform: skew(0.01deg);
  }
  60% {
    clip: rect(76px, 9999px, 41px, 0);
    transform: skew(0.15deg);
  }
  65% {
    clip: rect(3px, 9999px, 68px, 0);
    transform: skew(0.55deg);
  }
  70% {
    clip: rect(24px, 9999px, 30px, 0);
    transform: skew(0.31deg);
  }
  75% {
    clip: rect(63px, 9999px, 43px, 0);
    transform: skew(0.76deg);
  }
  80% {
    clip: rect(23px, 9999px, 60px, 0);
    transform: skew(0.98deg);
  }
  85% {
    clip: rect(54px, 9999px, 35px, 0);
    transform: skew(0.64deg);
  }
  90% {
    clip: rect(35px, 9999px, 95px, 0);
    transform: skew(0.37deg);
  }
  95% {
    clip: rect(54px, 9999px, 50px, 0);
    transform: skew(0.56deg);
  }
  100% {
    clip: rect(74px, 9999px, 34px, 0);
    transform: skew(0.22deg);
  }
}

@keyframes glitch-skew {
  0% {
    transform: skew(-1deg);
  }
  10% {
    transform: skew(0.5deg);
  }
  20% {
    transform: skew(0.9deg);
  }
  30% {
    transform: skew(0.3deg);
  }
  40% {
    transform: skew(-0.5deg);
  }
  50% {
    transform: skew(-1deg);
  }
  60% {
    transform: skew(0.7deg);
  }
  70% {
    transform: skew(0.2deg);
  }
  80% {
    transform: skew(-0.2deg);
  }
  90% {
    transform: skew(0.5deg);
  }
  100% {
    transform: skew(-0.1deg);
  }
}

/* Scanline effect */
.scanline {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    to bottom,
    transparent 50%,
    rgba(0, 255, 255, 0.02) 50%
  );
  background-size: 100% 4px;
  z-index: 9998;
  pointer-events: none;
  opacity: 0.15;
}

/* CRT flicker */
.crt-flicker {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(18, 16, 16, 0);
  opacity: 0;
  z-index: 9999;
  pointer-events: none;
  animation: flicker 0.3s infinite;
}

@keyframes flicker {
  0% {
    opacity: 0.1;
  }
  2% {
    opacity: 0.1;
  }
  4% {
    opacity: 0.05;
  }
  6% {
    opacity: 0.1;
  }
  8% {
    opacity: 0.05;
  }
  10% {
    opacity: 0.02;
  }
  20% {
    opacity: 0.05;
  }
  30% {
    opacity: 0.02;
  }
  40% {
    opacity: 0.06;
  }
  50% {
    opacity: 0.05;
  }
  60% {
    opacity: 0.02;
  }
  70% {
    opacity: 0.09;
  }
  80% {
    opacity: 0.03;
  }
  90% {
    opacity: 0.06;
  }
  100% {
    opacity: 0.1;
  }
}

/* Hacking text effect */
.hacking-animation {
  overflow: hidden;
  white-space: nowrap;
  animation: typing 3.5s steps(40, end);
}

@keyframes typing {
  from { width: 0 }
  to { width: 100% }
}
