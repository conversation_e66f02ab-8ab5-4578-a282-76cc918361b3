import { Router } from 'express';
import { body } from 'express-validator';
import {
  createContactMessage,
  getContactMessages,
  getContactMessage,
  markAsRead,
  markAsReplied,
  deleteContactMessage,
  getContactStats,
} from '@/controllers/contactController';
import { authenticate, authorize } from '@/middleware/auth';
import { UserRole, VALIDATION_RULES } from '@portfolio/shared';

const router = Router();

// Validation rules for contact form
const contactValidation = [
  body('name')
    .trim()
    .isLength({ min: 2, max: VALIDATION_RULES.NAME_MAX_LENGTH })
    .withMessage(`Name must be between 2 and ${VALIDATION_RULES.NAME_MAX_LENGTH} characters`),
  body('email')
    .isEmail()
    .normalizeEmail()
    .isLength({ max: VALIDATION_RULES.EMAIL_MAX_LENGTH })
    .withMessage('Please provide a valid email address'),
  body('subject')
    .trim()
    .isLength({ min: 5, max: VALIDATION_RULES.SUBJECT_MAX_LENGTH })
    .withMessage(`Subject must be between 5 and ${VALIDATION_RULES.SUBJECT_MAX_LENGTH} characters`),
  body('message')
    .trim()
    .isLength({ min: 10, max: VALIDATION_RULES.MESSAGE_MAX_LENGTH })
    .withMessage(`Message must be between 10 and ${VALIDATION_RULES.MESSAGE_MAX_LENGTH} characters`),
];

// Public routes
router.post('/', contactValidation, createContactMessage);

// Protected routes (admin only)
router.use(authenticate);
router.use(authorize(UserRole.ADMIN));

router.get('/', getContactMessages);
router.get('/stats', getContactStats);
router.get('/:id', getContactMessage);
router.patch('/:id/read', markAsRead);
router.patch('/:id/replied', markAsReplied);
router.delete('/:id', deleteContactMessage);

export default router;
