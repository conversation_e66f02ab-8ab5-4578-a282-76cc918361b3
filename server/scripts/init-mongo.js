// MongoDB initialization script for Docker
db = db.getSiblingDB('portfolio');

// Create collections
db.createCollection('users');
db.createCollection('contactmessages');
db.createCollection('projects');
db.createCollection('analytics');

// Create indexes
db.users.createIndex({ email: 1 }, { unique: true });
db.contactmessages.createIndex({ email: 1 });
db.contactmessages.createIndex({ createdAt: -1 });
db.projects.createIndex({ featured: 1 });
db.projects.createIndex({ category: 1 });
db.analytics.createIndex({ event: 1 });
db.analytics.createIndex({ timestamp: -1 });

print('Database initialized successfully');
