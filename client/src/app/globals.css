@tailwind base;
@tailwind components;
@tailwind utilities;

/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Fira+Code:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;600;700;800;900&display=swap');

@layer base {
  :root {
    --background: 220 10% 4%;
    --foreground: 0 0% 98%;
    --card: 220 10% 7%;
    --card-foreground: 0 0% 98%;
    --popover: 220 10% 7%;
    --popover-foreground: 0 0% 98%;
    --primary: 180 100% 50%;
    --primary-foreground: 220 10% 4%;
    --secondary: 220 10% 15%;
    --secondary-foreground: 0 0% 98%;
    --muted: 220 10% 15%;
    --muted-foreground: 0 0% 60%;
    --accent: 280 100% 25%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 84% 37%;
    --destructive-foreground: 0 0% 98%;
    --border: 220 10% 20%;
    --input: 220 10% 15%;
    --ring: 180 100% 50%;
    --radius: 0.5rem;
  }

  * {
    @apply border-border;
  }

  body {
    @apply bg-darkweb-bg text-darkweb-text font-mono;
    background-image: 
      radial-gradient(circle at 25% 25%, rgba(110, 11, 117, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 75% 75%, rgba(0, 255, 255, 0.05) 0%, transparent 50%),
      url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%2300ff00' fill-opacity='0.02'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    background-attachment: fixed;
  }

  html {
    scroll-behavior: smooth;
  }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-darkweb-surface;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-darkweb-accent rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-darkweb-purple;
  }

  /* Selection */
  ::selection {
    @apply bg-darkweb-accent text-darkweb-bg;
  }

  ::-moz-selection {
    @apply bg-darkweb-accent text-darkweb-bg;
  }
}

@layer components {
  /* Terminal Window */
  .terminal-window {
    @apply bg-black/90 border border-darkweb-border rounded-lg overflow-hidden backdrop-blur-sm;
    box-shadow: 
      0 0 20px rgba(0, 255, 255, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
  }

  .terminal-header {
    @apply flex items-center justify-between px-4 py-2 bg-darkweb-surface border-b border-darkweb-border;
  }

  .terminal-buttons {
    @apply flex space-x-2;
  }

  .terminal-button {
    @apply w-3 h-3 rounded-full;
  }

  .terminal-button.close {
    @apply bg-red-500;
  }

  .terminal-button.minimize {
    @apply bg-yellow-500;
  }

  .terminal-button.maximize {
    @apply bg-green-500;
  }

  .terminal-title {
    @apply text-sm font-mono text-darkweb-accent;
  }

  .terminal-body {
    @apply p-4 font-mono text-sm;
  }

  .terminal-line {
    @apply text-darkweb-green mb-2;
  }

  .terminal-line::before {
    content: "$ ";
    @apply text-darkweb-accent;
  }

  /* Glitch Text Effect */
  .glitch-text {
    @apply relative inline-block;
    animation: glitch-skew 1s infinite linear alternate-reverse;
  }

  .glitch-text::before,
  .glitch-text::after {
    content: attr(data-text);
    @apply absolute top-0 left-0 w-full h-full;
  }

  .glitch-text::before {
    left: 2px;
    text-shadow: -2px 0 theme(colors.darkweb.purple);
    clip: rect(44px, 450px, 56px, 0);
    animation: glitch 5s infinite linear alternate-reverse;
  }

  .glitch-text::after {
    left: -2px;
    text-shadow: -2px 0 theme(colors.darkweb.accent);
    clip: rect(44px, 450px, 56px, 0);
    animation: glitch 5s infinite linear alternate-reverse;
  }

  /* Button Styles */
  .btn-terminal {
    @apply px-4 py-2 bg-transparent border border-darkweb-accent text-darkweb-accent font-mono text-sm rounded transition-all duration-300 hover:bg-darkweb-accent hover:text-darkweb-bg hover:shadow-lg hover:shadow-darkweb-accent/25;
  }

  .btn-primary {
    @apply px-6 py-3 bg-darkweb-accent text-darkweb-bg font-mono font-medium rounded transition-all duration-300 hover:bg-darkweb-purple hover:shadow-lg hover:shadow-darkweb-accent/25;
  }

  .btn-secondary {
    @apply px-6 py-3 bg-transparent border border-darkweb-border text-darkweb-text font-mono font-medium rounded transition-all duration-300 hover:border-darkweb-accent hover:text-darkweb-accent;
  }

  /* Card Styles */
  .card-dark {
    @apply bg-darkweb-card border border-darkweb-border rounded-lg p-6 backdrop-blur-sm;
    box-shadow: 
      0 4px 6px -1px rgba(0, 0, 0, 0.3),
      0 0 15px rgba(110, 11, 117, 0.1);
  }

  .card-glow {
    background: rgba(26, 26, 26, 1);
    border: 1px solid rgba(51, 51, 51, 1);
    border-radius: 0.5rem;
    padding: 1.5rem;
    backdrop-filter: blur(2px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 0 15px rgba(110, 11, 117, 0.1);
    transition: all 0.3s ease;
  }

  .card-glow:hover {
    box-shadow: 0 10px 15px -3px rgba(0, 255, 255, 0.1);
    border-color: rgba(0, 255, 255, 0.5);
  }

  /* Input Styles */
  .input-terminal {
    @apply w-full px-4 py-2 bg-black/50 border border-darkweb-border text-darkweb-text font-mono text-sm rounded focus:outline-none focus:border-darkweb-accent focus:ring-1 focus:ring-darkweb-accent transition-colors;
  }

  .input-terminal::placeholder {
    @apply text-darkweb-muted;
  }

  /* Skill Bar */
  .skill-bar {
    @apply w-full h-2 bg-darkweb-surface rounded-full overflow-hidden;
  }

  .skill-progress {
    @apply h-full bg-gradient-to-r from-darkweb-accent to-darkweb-purple rounded-full transition-all duration-1000 ease-out;
    box-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
  }

  /* Project Card */
  .project-card {
    @apply cursor-pointer overflow-hidden;
    background: rgba(26, 26, 26, 1);
    border: 1px solid rgba(51, 51, 51, 1);
    border-radius: 0.5rem;
    padding: 1.5rem;
    backdrop-filter: blur(2px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 0 15px rgba(110, 11, 117, 0.1);
    transition: all 0.3s ease;
  }

  .project-card:hover {
    box-shadow: 0 10px 15px -3px rgba(0, 255, 255, 0.1), 0 0 15px rgba(110, 11, 117, 0.1);
    border-color: rgba(0, 255, 255, 0.5);
  }

  .project-image {
    @apply w-full h-48 object-cover transition-transform duration-300 group-hover:scale-105;
  }

  .project-overlay {
    @apply absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center;
  }

  /* Tech Tag */
  .tech-tag {
    @apply px-2 py-1 bg-darkweb-surface text-darkweb-accent text-xs font-mono rounded border border-darkweb-border transition-all duration-300 hover:border-darkweb-accent hover:shadow-sm hover:shadow-darkweb-accent/25;
  }

  /* Loading Spinner */
  .loading-spinner {
    @apply w-6 h-6 border-2 border-darkweb-border border-t-darkweb-accent rounded-full animate-spin;
  }

  /* Matrix Rain Effect */
  .matrix-rain {
    @apply absolute inset-0 overflow-hidden pointer-events-none;
  }

  .matrix-char {
    @apply absolute text-darkweb-green font-mono text-sm opacity-70;
    animation: matrix-rain 20s linear infinite;
  }

  /* Scanline Effect */
  .scanline-overlay {
    @apply fixed inset-0 pointer-events-none z-50;
    background: linear-gradient(
      to bottom,
      transparent 50%,
      rgba(0, 255, 255, 0.02) 50%
    );
    background-size: 100% 4px;
    opacity: 0.15;
  }

  /* CRT Flicker */
  .crt-flicker {
    @apply fixed inset-0 pointer-events-none z-40;
    background: rgba(18, 16, 16, 0);
    opacity: 0;
    animation: flicker 0.3s infinite;
  }

  /* Typing Animation */
  .typing-animation {
    @apply overflow-hidden whitespace-nowrap border-r-2 border-darkweb-accent;
    animation: typing 3.5s steps(40, end), blink 1s infinite;
  }

  /* Glow Effect */
  .glow-accent {
    box-shadow: 0 0 20px rgba(0, 255, 255, 0.3);
  }

  .glow-purple {
    box-shadow: 0 0 20px rgba(110, 11, 117, 0.3);
  }

  /* Responsive Grid */
  .grid-responsive {
    @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6;
  }

  /* Section Padding */
  .section-padding {
    @apply py-16 md:py-24;
  }

  /* Container */
  .container-custom {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }
}

@layer utilities {
  .text-shadow-glow {
    text-shadow: 0 0 8px rgba(0, 255, 255, 0.7);
  }

  .text-shadow-purple {
    text-shadow: 0 0 8px rgba(110, 11, 117, 0.7);
  }

  .backdrop-blur-xs {
    backdrop-filter: blur(2px);
  }

  .animate-delay-100 {
    animation-delay: 100ms;
  }

  .animate-delay-200 {
    animation-delay: 200ms;
  }

  .animate-delay-300 {
    animation-delay: 300ms;
  }

  .animate-delay-500 {
    animation-delay: 500ms;
  }
}
