# Server Environment Variables
# Copy this file to .env and fill in your actual values

# Server Configuration
NODE_ENV=development
PORT=5000
HOST=localhost

# Database
MONGODB_URI=mongodb://localhost:27017/portfolio
MONGODB_TEST_URI=mongodb://localhost:27017/portfolio_test

# Redis
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=

# JWT
JWT_SECRET=your-super-secret-jwt-key-minimum-32-characters
JWT_EXPIRES_IN=7d
JWT_REFRESH_EXPIRES_IN=30d

# Email Configuration
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-specific-password
EMAIL_FROM=<EMAIL>

# Client Configuration
CLIENT_URL=http://localhost:3000
ALLOWED_ORIGINS=http://localhost:3000,https://thestarsahil.me

# File Upload
UPLOAD_MAX_SIZE=5242880
UPLOAD_ALLOWED_TYPES=image/jpeg,image/png,image/webp,application/pdf

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Security
BCRYPT_ROUNDS=12
SESSION_SECRET=your-session-secret-key

# External APIs
GITHUB_TOKEN=your-github-personal-access-token
GOOGLE_ANALYTICS_API_KEY=your-google-analytics-api-key

# Logging
LOG_LEVEL=info
LOG_FILE=logs/app.log

# Admin User (for seeding)
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=your-admin-password
ADMIN_NAME=Sahil Ali
