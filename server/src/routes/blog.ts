import { Router } from 'express';
import { HTTP_STATUS } from '@portfolio/shared';

const router = Router();

// Mock blog data
const mockBlogPosts = [
  {
    id: '1',
    title: 'Getting Started with Cybersecurity',
    slug: 'getting-started-cybersecurity',
    excerpt: 'A beginner\'s guide to cybersecurity fundamentals',
    content: 'Full blog post content here...',
    tags: ['cybersecurity', 'beginner', 'tutorial'],
    category: 'cybersecurity',
    published: true,
    publishedAt: '2024-01-15',
    readTime: 5,
    views: 150,
    likes: 25,
  },
  {
    id: '2',
    title: 'Building AI Models with Python',
    slug: 'building-ai-models-python',
    excerpt: 'Learn how to build and train AI models using Python',
    content: 'Full blog post content here...',
    tags: ['ai', 'python', 'machine-learning'],
    category: 'ai-ml',
    published: true,
    publishedAt: '2024-01-10',
    readTime: 8,
    views: 200,
    likes: 35,
  },
];

// Get all blog posts
router.get('/', (req, res) => {
  const { category, tag, published = 'true' } = req.query;
  
  let filteredPosts = [...mockBlogPosts];
  
  if (published === 'true') {
    filteredPosts = filteredPosts.filter(p => p.published);
  }
  
  if (category) {
    filteredPosts = filteredPosts.filter(p => p.category === category);
  }
  
  if (tag) {
    filteredPosts = filteredPosts.filter(p => p.tags.includes(tag as string));
  }

  res.status(HTTP_STATUS.OK).json({
    success: true,
    data: filteredPosts,
    timestamp: new Date().toISOString(),
  });
});

// Get blog post by slug
router.get('/:slug', (req, res) => {
  const post = mockBlogPosts.find(p => p.slug === req.params.slug);
  
  if (!post) {
    return res.status(HTTP_STATUS.NOT_FOUND).json({
      success: false,
      error: 'Blog post not found',
      timestamp: new Date().toISOString(),
    });
  }

  res.status(HTTP_STATUS.OK).json({
    success: true,
    data: post,
    timestamp: new Date().toISOString(),
  });
});

// Get blog categories
router.get('/meta/categories', (req, res) => {
  const categories = [...new Set(mockBlogPosts.map(p => p.category))];
  
  res.status(HTTP_STATUS.OK).json({
    success: true,
    data: categories,
    timestamp: new Date().toISOString(),
  });
});

// Get blog tags
router.get('/meta/tags', (req, res) => {
  const tags = [...new Set(mockBlogPosts.flatMap(p => p.tags))];
  
  res.status(HTTP_STATUS.OK).json({
    success: true,
    data: tags,
    timestamp: new Date().toISOString(),
  });
});

export default router;
