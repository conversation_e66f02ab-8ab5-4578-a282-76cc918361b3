'use client';

import { motion } from 'framer-motion';
import { Github, Linkedin, Youtube, Terminal } from 'lucide-react';
import Image from 'next/image';

export function Footer() {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-darkweb-surface border-t border-darkweb-border">
      {/* Footer Landscape */}
      <div className="relative overflow-hidden">
        <div className="absolute inset-0">
          <Image
            src="/assets/footer-landscape.jpg"
            alt="Dark Web Landscape"
            fill
            className="object-cover opacity-20"
          />
          <div className="absolute inset-0 bg-gradient-to-t from-darkweb-surface via-darkweb-surface/80 to-transparent" />
        </div>
        
        <div className="relative container-custom py-16">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center space-y-4"
          >
            <div className="glitch-text text-2xl md:text-3xl font-bold text-darkweb-accent" data-text="THANK YOU FOR VISITING">
              THANK YOU FOR VISITING
            </div>
            <div className="text-darkweb-muted">Connection Terminated Safely</div>
            <div className="terminal-line text-darkweb-green">exit --secure</div>
          </motion.div>
        </div>
      </div>

      {/* Footer Content */}
      <div className="container-custom py-8">
        <div className="flex flex-col md:flex-row items-center justify-between gap-6">
          {/* Copyright */}
          <div className="text-center md:text-left">
            <p className="text-darkweb-muted text-sm">
              Created with{' '}
              <span className="text-darkweb-accent">Linux Community</span> |{' '}
              <span className="text-darkweb-muted">© {currentYear} All rights reserved.</span>
            </p>
          </div>

          {/* Social Links */}
          <div className="flex items-center gap-4">
            <a
              href="https://github.com/thestarsahil"
              target="_blank"
              rel="noopener noreferrer"
              className="p-2 text-darkweb-muted hover:text-darkweb-accent transition-colors hover:scale-110 transform"
              aria-label="GitHub"
            >
              <Github className="w-5 h-5" />
            </a>
            <a
              href="https://www.linkedin.com/in/thestarsahil/"
              target="_blank"
              rel="noopener noreferrer"
              className="p-2 text-darkweb-muted hover:text-darkweb-accent transition-colors hover:scale-110 transform"
              aria-label="LinkedIn"
            >
              <Linkedin className="w-5 h-5" />
            </a>
            <a
              href="https://www.youtube.com/@thestarsahil"
              target="_blank"
              rel="noopener noreferrer"
              className="p-2 text-darkweb-muted hover:text-darkweb-accent transition-colors hover:scale-110 transform"
              aria-label="YouTube"
            >
              <Youtube className="w-5 h-5" />
            </a>
          </div>

          {/* Onion Address */}
          <div className="flex items-center gap-2 text-sm">
            <img src="/assets/tor-logo.svg" alt="Tor Network" className="w-4 h-4" />
            <span className="glitch-text text-darkweb-accent font-mono" data-text="sahil3xvt7rjgd5woap4qd.onion">
              sahil3xvt7rjgd5woap4qd.onion
            </span>
          </div>
        </div>
      </div>
    </footer>
  );
}
