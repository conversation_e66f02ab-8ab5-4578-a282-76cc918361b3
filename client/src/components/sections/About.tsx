'use client';

import { motion } from 'framer-motion';
import Image from 'next/image';
import { Download, Award, Code, Zap } from 'lucide-react';

export function About() {
  return (
    <section id="about" className="section-padding bg-darkweb-surface/30">
      <div className="container-custom">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl md:text-4xl font-bold text-darkweb-text mb-4">
            About <span className="text-darkweb-accent glitch-text" data-text="me">me</span>
          </h2>
          <div className="w-24 h-1 bg-darkweb-accent mx-auto"></div>
        </motion.div>

        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Image */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="relative"
          >
            <div className="relative w-full max-w-md mx-auto">
              <div className="absolute inset-0 bg-darkweb-accent/20 rounded-lg transform rotate-6"></div>
              <div className="relative bg-darkweb-card rounded-lg p-4 border border-darkweb-border">
                <Image
                  src="/assets/profile.jpg"
                  alt="Sahil Ali"
                  width={400}
                  height={500}
                  className="w-full h-auto rounded-lg"
                  priority
                />
              </div>
            </div>
          </motion.div>

          {/* Content */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="space-y-6"
          >
            <div className="terminal-window">
              <div className="terminal-header">
                <div className="terminal-buttons">
                  <span className="terminal-button close"></span>
                  <span className="terminal-button minimize"></span>
                  <span className="terminal-button maximize"></span>
                </div>
                <div className="terminal-title">about.txt</div>
              </div>
              <div className="terminal-body">
                <div className="space-y-4">
                  <p className="text-darkweb-text leading-relaxed">
                    I'm Sahil and I'm a{' '}
                    <span className="text-darkweb-accent font-bold typing-animation">
                      Full Stack Developer
                    </span>
                  </p>
                  
                  <div className="space-y-2">
                    <div className="flex items-center gap-2 text-darkweb-green">
                      <Code className="w-4 h-4" />
                      <span>Open Source Programmer</span>
                    </div>
                    <div className="flex items-center gap-2 text-darkweb-purple">
                      <Award className="w-4 h-4" />
                      <span>Ex-Microsoft MLSA</span>
                    </div>
                    <div className="flex items-center gap-2 text-darkweb-accent">
                      <Zap className="w-4 h-4" />
                      <span>Cybersecurity Enthusiast</span>
                    </div>
                  </div>

                  <p className="text-darkweb-muted text-sm leading-relaxed">
                    Computer Science Engineer with expertise in full-stack development, 
                    competitive programming, and cybersecurity. Passionate about creating 
                    innovative solutions and contributing to open-source projects.
                  </p>
                </div>
              </div>
            </div>

            <motion.a
              href="/Resume Sahil 2024.pdf"
              download
              className="btn-primary inline-flex items-center gap-2"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Download className="w-4 h-4" />
              Download CV
            </motion.a>
          </motion.div>
        </div>
      </div>
    </section>
  );
}
