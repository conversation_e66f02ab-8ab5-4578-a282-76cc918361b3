globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/_not-found/page"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"(app-pages-browser)/./src/components/layout/Footer.tsx":{"*":{"id":"(ssr)/./src/components/layout/Footer.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/layout/Navbar.tsx":{"*":{"id":"(ssr)/./src/components/layout/Navbar.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/About.tsx":{"*":{"id":"(ssr)/./src/components/sections/About.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/Contact.tsx":{"*":{"id":"(ssr)/./src/components/sections/Contact.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/Hero.tsx":{"*":{"id":"(ssr)/./src/components/sections/Hero.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/Projects.tsx":{"*":{"id":"(ssr)/./src/components/sections/Projects.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/Services.tsx":{"*":{"id":"(ssr)/./src/components/sections/Services.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/Skills.tsx":{"*":{"id":"(ssr)/./src/components/sections/Skills.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ui/ScrollToTop.tsx":{"*":{"id":"(ssr)/./src/components/ui/ScrollToTop.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs":{"*":{"id":"(ssr)/./node_modules/react-hot-toast/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/Analytics.tsx":{"*":{"id":"(ssr)/./src/components/Analytics.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/effects/CRTFlicker.tsx":{"*":{"id":"(ssr)/./src/components/effects/CRTFlicker.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/effects/ScanlineOverlay.tsx":{"*":{"id":"(ssr)/./src/components/effects/ScanlineOverlay.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/effects/ThreeBackground.tsx":{"*":{"id":"(ssr)/./src/components/effects/ThreeBackground.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/providers/index.tsx":{"*":{"id":"(ssr)/./src/components/providers/index.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/layout/Footer.tsx":{"id":"(app-pages-browser)/./src/components/layout/Footer.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/layout/Navbar.tsx":{"id":"(app-pages-browser)/./src/components/layout/Navbar.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/About.tsx":{"id":"(app-pages-browser)/./src/components/sections/About.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Contact.tsx":{"id":"(app-pages-browser)/./src/components/sections/Contact.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Hero.tsx":{"id":"(app-pages-browser)/./src/components/sections/Hero.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Projects.tsx":{"id":"(app-pages-browser)/./src/components/sections/Projects.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Services.tsx":{"id":"(app-pages-browser)/./src/components/sections/Services.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Skills.tsx":{"id":"(app-pages-browser)/./src/components/sections/Skills.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/ui/ScrollToTop.tsx":{"id":"(app-pages-browser)/./src/components/ui/ScrollToTop.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/home/<USER>/Documents/GitHub/Portfolio/client/node_modules/next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\",\"display\":\"swap\"}],\"variableName\":\"inter\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\",\"display\":\"swap\"}],\"variableName\":\"inter\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/home/<USER>/Documents/GitHub/Portfolio/client/node_modules/next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Fira_Code\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-fira-code\",\"display\":\"swap\"}],\"variableName\":\"firaCode\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Fira_Code\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-fira-code\",\"display\":\"swap\"}],\"variableName\":\"firaCode\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/home/<USER>/Documents/GitHub/Portfolio/client/node_modules/next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Orbitron\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-orbitron\",\"display\":\"swap\"}],\"variableName\":\"orbitron\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Orbitron\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-orbitron\",\"display\":\"swap\"}],\"variableName\":\"orbitron\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/home/<USER>/Documents/GitHub/Portfolio/client/node_modules/react-hot-toast/dist/index.mjs":{"id":"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/home/<USER>/Documents/GitHub/Portfolio/client/src/app/globals.css":{"id":"(app-pages-browser)/./src/app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/Analytics.tsx":{"id":"(app-pages-browser)/./src/components/Analytics.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/effects/CRTFlicker.tsx":{"id":"(app-pages-browser)/./src/components/effects/CRTFlicker.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/effects/ScanlineOverlay.tsx":{"id":"(app-pages-browser)/./src/components/effects/ScanlineOverlay.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/effects/ThreeBackground.tsx":{"id":"(app-pages-browser)/./src/components/effects/ThreeBackground.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/providers/index.tsx":{"id":"(app-pages-browser)/./src/components/providers/index.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/home/<USER>/Documents/GitHub/Portfolio/client/node_modules/next/dist/client/components/app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/home/<USER>/Documents/GitHub/Portfolio/client/node_modules/next/dist/esm/client/components/app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/home/<USER>/Documents/GitHub/Portfolio/client/node_modules/next/dist/client/components/client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/home/<USER>/Documents/GitHub/Portfolio/client/node_modules/next/dist/esm/client/components/client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/home/<USER>/Documents/GitHub/Portfolio/client/node_modules/next/dist/client/components/error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/home/<USER>/Documents/GitHub/Portfolio/client/node_modules/next/dist/esm/client/components/error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/home/<USER>/Documents/GitHub/Portfolio/client/node_modules/next/dist/client/components/layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/home/<USER>/Documents/GitHub/Portfolio/client/node_modules/next/dist/esm/client/components/layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/home/<USER>/Documents/GitHub/Portfolio/client/node_modules/next/dist/client/components/not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/home/<USER>/Documents/GitHub/Portfolio/client/node_modules/next/dist/esm/client/components/not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/home/<USER>/Documents/GitHub/Portfolio/client/node_modules/next/dist/client/components/render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/home/<USER>/Documents/GitHub/Portfolio/client/node_modules/next/dist/esm/client/components/render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false}},"entryCSSFiles":{"/home/<USER>/Documents/GitHub/Portfolio/client/src/":[],"/home/<USER>/Documents/GitHub/Portfolio/client/src/app/page":[],"/home/<USER>/Documents/GitHub/Portfolio/client/src/app/layout":["static/css/app/layout.css"],"/home/<USER>/Documents/GitHub/Portfolio/client/src/app/_not-found/page":[]}}