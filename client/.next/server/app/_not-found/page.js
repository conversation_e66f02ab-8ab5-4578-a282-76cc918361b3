/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/_not-found/page";
exports.ids = ["app/_not-found/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n          children: [\"/_not-found\", {\n            children: ['__PAGE__', {}, {\n              page: [\n                () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)),\n                \"next/dist/client/components/not-found-error\"\n              ]\n            }]\n          }, {}]\n        },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"/home/<USER>/Documents/GitHub/Portfolio/client/src/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/_not-found/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/_not-found/page\",\n        pathname: \"/_not-found\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRmhvbWUlMkZ0aGVzdGFyc2FoaWwlMkZEb2N1bWVudHMlMkZHaXRIdWIlMkZQb3J0Zm9saW8lMkZjbGllbnQlMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZGlzdCUyRmNsaWVudCUyRmNvbXBvbmVudHMlMkZhcHAtcm91dGVyLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRmhvbWUlMkZ0aGVzdGFyc2FoaWwlMkZEb2N1bWVudHMlMkZHaXRIdWIlMkZQb3J0Zm9saW8lMkZjbGllbnQlMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZGlzdCUyRmNsaWVudCUyRmNvbXBvbmVudHMlMkZjbGllbnQtcGFnZS5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjIlMkZob21lJTJGdGhlc3RhcnNhaGlsJTJGRG9jdW1lbnRzJTJGR2l0SHViJTJGUG9ydGZvbGlvJTJGY2xpZW50JTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmRpc3QlMkZjbGllbnQlMkZjb21wb25lbnRzJTJGZXJyb3ItYm91bmRhcnkuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGaG9tZSUyRnRoZXN0YXJzYWhpbCUyRkRvY3VtZW50cyUyRkdpdEh1YiUyRlBvcnRmb2xpbyUyRmNsaWVudCUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZkaXN0JTJGY2xpZW50JTJGY29tcG9uZW50cyUyRmxheW91dC1yb3V0ZXIuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGaG9tZSUyRnRoZXN0YXJzYWhpbCUyRkRvY3VtZW50cyUyRkdpdEh1YiUyRlBvcnRmb2xpbyUyRmNsaWVudCUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZkaXN0JTJGY2xpZW50JTJGY29tcG9uZW50cyUyRm5vdC1mb3VuZC1ib3VuZGFyeS5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjIlMkZob21lJTJGdGhlc3RhcnNhaGlsJTJGRG9jdW1lbnRzJTJGR2l0SHViJTJGUG9ydGZvbGlvJTJGY2xpZW50JTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmRpc3QlMkZjbGllbnQlMkZjb21wb25lbnRzJTJGcmVuZGVyLWZyb20tdGVtcGxhdGUtY29udGV4dC5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsa09BQWdKO0FBQ2hKO0FBQ0Esb09BQWlKO0FBQ2pKO0FBQ0EsME9BQW9KO0FBQ3BKO0FBQ0Esd09BQW1KO0FBQ25KO0FBQ0Esa1BBQXdKO0FBQ3hKO0FBQ0Esc1FBQWtLIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQHBvcnRmb2xpby9jbGllbnQvPzRjMTEiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvaG9tZS90aGVzdGFyc2FoaWwvRG9jdW1lbnRzL0dpdEh1Yi9Qb3J0Zm9saW8vY2xpZW50L25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvYXBwLXJvdXRlci5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL2hvbWUvdGhlc3RhcnNhaGlsL0RvY3VtZW50cy9HaXRIdWIvUG9ydGZvbGlvL2NsaWVudC9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL2NsaWVudC1wYWdlLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvaG9tZS90aGVzdGFyc2FoaWwvRG9jdW1lbnRzL0dpdEh1Yi9Qb3J0Zm9saW8vY2xpZW50L25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvZXJyb3ItYm91bmRhcnkuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9ob21lL3RoZXN0YXJzYWhpbC9Eb2N1bWVudHMvR2l0SHViL1BvcnRmb2xpby9jbGllbnQvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9sYXlvdXQtcm91dGVyLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvaG9tZS90aGVzdGFyc2FoaWwvRG9jdW1lbnRzL0dpdEh1Yi9Qb3J0Zm9saW8vY2xpZW50L25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvbm90LWZvdW5kLWJvdW5kYXJ5LmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvaG9tZS90aGVzdGFyc2FoaWwvRG9jdW1lbnRzL0dpdEh1Yi9Qb3J0Zm9saW8vY2xpZW50L25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvcmVuZGVyLWZyb20tdGVtcGxhdGUtY29udGV4dC5qc1wiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Fira_Code%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-fira-code%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22firaCode%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Orbitron%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-orbitron%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22orbitron%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fnode_modules%2Freact-hot-toast%2Fdist%2Findex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fsrc%2Fcomponents%2FAnalytics.tsx%22%2C%22ids%22%3A%5B%22Analytics%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fsrc%2Fcomponents%2Feffects%2FCRTFlicker.tsx%22%2C%22ids%22%3A%5B%22CRTFlicker%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fsrc%2Fcomponents%2Feffects%2FScanlineOverlay.tsx%22%2C%22ids%22%3A%5B%22ScanlineOverlay%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fsrc%2Fcomponents%2Feffects%2FThreeBackground.tsx%22%2C%22ids%22%3A%5B%22ThreeBackground%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fsrc%2Fcomponents%2Fproviders%2Findex.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Fira_Code%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-fira-code%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22firaCode%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Orbitron%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-orbitron%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22orbitron%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fnode_modules%2Freact-hot-toast%2Fdist%2Findex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fsrc%2Fcomponents%2FAnalytics.tsx%22%2C%22ids%22%3A%5B%22Analytics%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fsrc%2Fcomponents%2Feffects%2FCRTFlicker.tsx%22%2C%22ids%22%3A%5B%22CRTFlicker%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fsrc%2Fcomponents%2Feffects%2FScanlineOverlay.tsx%22%2C%22ids%22%3A%5B%22ScanlineOverlay%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fsrc%2Fcomponents%2Feffects%2FThreeBackground.tsx%22%2C%22ids%22%3A%5B%22ThreeBackground%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fsrc%2Fcomponents%2Fproviders%2Findex.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/react-hot-toast/dist/index.mjs */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Analytics.tsx */ \"(ssr)/./src/components/Analytics.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/effects/CRTFlicker.tsx */ \"(ssr)/./src/components/effects/CRTFlicker.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/effects/ScanlineOverlay.tsx */ \"(ssr)/./src/components/effects/ScanlineOverlay.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/effects/ThreeBackground.tsx */ \"(ssr)/./src/components/effects/ThreeBackground.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers/index.tsx */ \"(ssr)/./src/components/providers/index.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Fira_Code%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-fira-code%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22firaCode%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Orbitron%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-orbitron%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22orbitron%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fnode_modules%2Freact-hot-toast%2Fdist%2Findex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fsrc%2Fcomponents%2FAnalytics.tsx%22%2C%22ids%22%3A%5B%22Analytics%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fsrc%2Fcomponents%2Feffects%2FCRTFlicker.tsx%22%2C%22ids%22%3A%5B%22CRTFlicker%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fsrc%2Fcomponents%2Feffects%2FScanlineOverlay.tsx%22%2C%22ids%22%3A%5B%22ScanlineOverlay%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fsrc%2Fcomponents%2Feffects%2FThreeBackground.tsx%22%2C%22ids%22%3A%5B%22ThreeBackground%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fsrc%2Fcomponents%2Fproviders%2Findex.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/components/Analytics.tsx":
/*!**************************************!*\
  !*** ./src/components/Analytics.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Analytics: () => (/* binding */ Analytics),\n/* harmony export */   trackContactForm: () => (/* binding */ trackContactForm),\n/* harmony export */   trackEvent: () => (/* binding */ trackEvent),\n/* harmony export */   trackPageView: () => (/* binding */ trackPageView),\n/* harmony export */   trackProjectView: () => (/* binding */ trackProjectView),\n/* harmony export */   trackResumeDownload: () => (/* binding */ trackResumeDownload),\n/* harmony export */   trackSocialClick: () => (/* binding */ trackSocialClick)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ Analytics,trackEvent,trackPageView,trackContactForm,trackProjectView,trackResumeDownload,trackSocialClick auto */ \n\nfunction Analytics() {\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.usePathname)();\n    const GA_TRACKING_ID = \"G-XXXXXXXXXX\";\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (!GA_TRACKING_ID) return;\n        // Load Google Analytics script\n        const script = document.createElement(\"script\");\n        script.src = `https://www.googletagmanager.com/gtag/js?id=${GA_TRACKING_ID}`;\n        script.async = true;\n        document.head.appendChild(script);\n        // Initialize gtag\n        window.gtag = function gtag() {\n            // eslint-disable-next-line prefer-rest-params\n            window.dataLayer = window.dataLayer || [];\n            // eslint-disable-next-line prefer-rest-params\n            window.dataLayer.push(arguments);\n        };\n        window.gtag(\"js\", new Date());\n        window.gtag(\"config\", GA_TRACKING_ID, {\n            page_path: pathname\n        });\n        return ()=>{\n            document.head.removeChild(script);\n        };\n    }, [\n        GA_TRACKING_ID\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (!GA_TRACKING_ID || !window.gtag) return;\n        window.gtag(\"config\", GA_TRACKING_ID, {\n            page_path: pathname\n        });\n    }, [\n        pathname,\n        GA_TRACKING_ID\n    ]);\n    return null;\n}\n// Analytics tracking functions\nconst trackEvent = (action, category, label, value)=>{\n    if (false) {}\n};\nconst trackPageView = (url)=>{\n    if (false) {}\n};\nconst trackContactForm = ()=>{\n    trackEvent(\"submit\", \"contact_form\", \"contact_page\");\n};\nconst trackProjectView = (projectName)=>{\n    trackEvent(\"view\", \"project\", projectName);\n};\nconst trackResumeDownload = ()=>{\n    trackEvent(\"download\", \"resume\", \"header_button\");\n};\nconst trackSocialClick = (platform)=>{\n    trackEvent(\"click\", \"social_media\", platform);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Analytics.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/effects/CRTFlicker.tsx":
/*!***********************************************!*\
  !*** ./src/components/effects/CRTFlicker.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CRTFlicker: () => (/* binding */ CRTFlicker)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ CRTFlicker auto */ \nfunction CRTFlicker() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"crt-flicker\"\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/effects/CRTFlicker.tsx\",\n        lineNumber: 4,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9lZmZlY3RzL0NSVEZsaWNrZXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFFTyxTQUFTQTtJQUNkLHFCQUFPLDhEQUFDQztRQUFJQyxXQUFVOzs7Ozs7QUFDeEIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9AcG9ydGZvbGlvL2NsaWVudC8uL3NyYy9jb21wb25lbnRzL2VmZmVjdHMvQ1JURmxpY2tlci50c3g/YTE1MiJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmV4cG9ydCBmdW5jdGlvbiBDUlRGbGlja2VyKCkge1xuICByZXR1cm4gPGRpdiBjbGFzc05hbWU9XCJjcnQtZmxpY2tlclwiIC8+O1xufVxuIl0sIm5hbWVzIjpbIkNSVEZsaWNrZXIiLCJkaXYiLCJjbGFzc05hbWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/effects/CRTFlicker.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/effects/ScanlineOverlay.tsx":
/*!****************************************************!*\
  !*** ./src/components/effects/ScanlineOverlay.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ScanlineOverlay: () => (/* binding */ ScanlineOverlay)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ ScanlineOverlay auto */ \nfunction ScanlineOverlay() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"scanline-overlay\"\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/effects/ScanlineOverlay.tsx\",\n        lineNumber: 4,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9lZmZlY3RzL1NjYW5saW5lT3ZlcmxheS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUVPLFNBQVNBO0lBQ2QscUJBQU8sOERBQUNDO1FBQUlDLFdBQVU7Ozs7OztBQUN4QiIsInNvdXJjZXMiOlsid2VicGFjazovL0Bwb3J0Zm9saW8vY2xpZW50Ly4vc3JjL2NvbXBvbmVudHMvZWZmZWN0cy9TY2FubGluZU92ZXJsYXkudHN4PzkxMTkiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5leHBvcnQgZnVuY3Rpb24gU2NhbmxpbmVPdmVybGF5KCkge1xuICByZXR1cm4gPGRpdiBjbGFzc05hbWU9XCJzY2FubGluZS1vdmVybGF5XCIgLz47XG59XG4iXSwibmFtZXMiOlsiU2NhbmxpbmVPdmVybGF5IiwiZGl2IiwiY2xhc3NOYW1lIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/effects/ScanlineOverlay.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/effects/ThreeBackground.tsx":
/*!****************************************************!*\
  !*** ./src/components/effects/ThreeBackground.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThreeBackground: () => (/* binding */ ThreeBackground)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var three__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! three */ \"(ssr)/./node_modules/three/build/three.module.js\");\n/* __next_internal_client_entry_do_not_use__ ThreeBackground auto */ \n\n\nfunction ThreeBackground() {\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const sceneRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    const rendererRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    const cameraRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    const particlesRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    const animationIdRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!canvasRef.current) return;\n        // Scene setup\n        const scene = new three__WEBPACK_IMPORTED_MODULE_2__.Scene();\n        sceneRef.current = scene;\n        // Camera setup\n        const camera = new three__WEBPACK_IMPORTED_MODULE_2__.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 2000);\n        camera.position.z = 1000;\n        cameraRef.current = camera;\n        // Renderer setup\n        const renderer = new three__WEBPACK_IMPORTED_MODULE_2__.WebGLRenderer({\n            canvas: canvasRef.current,\n            alpha: true,\n            antialias: true\n        });\n        renderer.setSize(window.innerWidth, window.innerHeight);\n        renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));\n        rendererRef.current = renderer;\n        // Create particle system\n        const particleCount = 5000;\n        const positions = new Float32Array(particleCount * 3);\n        const colors = new Float32Array(particleCount * 3);\n        const velocities = new Float32Array(particleCount * 3);\n        // Initialize particles\n        for(let i = 0; i < particleCount; i++){\n            const i3 = i * 3;\n            // Positions\n            positions[i3] = (Math.random() - 0.5) * 2000;\n            positions[i3 + 1] = (Math.random() - 0.5) * 2000;\n            positions[i3 + 2] = (Math.random() - 0.5) * 2000;\n            // Colors (cyan to purple gradient)\n            const colorChoice = Math.random();\n            if (colorChoice < 0.6) {\n                // Cyan\n                colors[i3] = 0;\n                colors[i3 + 1] = 1;\n                colors[i3 + 2] = 1;\n            } else if (colorChoice < 0.8) {\n                // Purple\n                colors[i3] = 0.43;\n                colors[i3 + 1] = 0.04;\n                colors[i3 + 2] = 0.46;\n            } else {\n                // Green\n                colors[i3] = 0;\n                colors[i3 + 1] = 1;\n                colors[i3 + 2] = 0;\n            }\n            // Velocities\n            velocities[i3] = (Math.random() - 0.5) * 0.5;\n            velocities[i3 + 1] = (Math.random() - 0.5) * 0.5;\n            velocities[i3 + 2] = (Math.random() - 0.5) * 0.5;\n        }\n        const geometry = new three__WEBPACK_IMPORTED_MODULE_2__.BufferGeometry();\n        geometry.setAttribute(\"position\", new three__WEBPACK_IMPORTED_MODULE_2__.BufferAttribute(positions, 3));\n        geometry.setAttribute(\"color\", new three__WEBPACK_IMPORTED_MODULE_2__.BufferAttribute(colors, 3));\n        const material = new three__WEBPACK_IMPORTED_MODULE_2__.PointsMaterial({\n            size: 2,\n            vertexColors: true,\n            transparent: true,\n            opacity: 0.8,\n            blending: three__WEBPACK_IMPORTED_MODULE_2__.AdditiveBlending\n        });\n        const particles = new three__WEBPACK_IMPORTED_MODULE_2__.Points(geometry, material);\n        scene.add(particles);\n        particlesRef.current = particles;\n        // Mouse interaction\n        let mouseX = 0;\n        let mouseY = 0;\n        const handleMouseMove = (event)=>{\n            mouseX = event.clientX / window.innerWidth * 2 - 1;\n            mouseY = -(event.clientY / window.innerHeight) * 2 + 1;\n        };\n        window.addEventListener(\"mousemove\", handleMouseMove);\n        // Animation loop\n        const animate = ()=>{\n            animationIdRef.current = requestAnimationFrame(animate);\n            if (particles && particles.geometry.attributes.position) {\n                const positions = particles.geometry.attributes.position.array;\n                // Update particle positions\n                for(let i = 0; i < particleCount; i++){\n                    const i3 = i * 3;\n                    // Move particles\n                    positions[i3] += velocities[i3];\n                    positions[i3 + 1] += velocities[i3 + 1];\n                    positions[i3 + 2] += velocities[i3 + 2];\n                    // Wrap around screen\n                    if (positions[i3] > 1000) positions[i3] = -1000;\n                    if (positions[i3] < -1000) positions[i3] = 1000;\n                    if (positions[i3 + 1] > 1000) positions[i3 + 1] = -1000;\n                    if (positions[i3 + 1] < -1000) positions[i3 + 1] = 1000;\n                    if (positions[i3 + 2] > 1000) positions[i3 + 2] = -1000;\n                    if (positions[i3 + 2] < -1000) positions[i3 + 2] = 1000;\n                }\n                particles.geometry.attributes.position.needsUpdate = true;\n            }\n            // Rotate particles based on mouse\n            if (particles) {\n                particles.rotation.x += mouseY * 0.0001;\n                particles.rotation.y += mouseX * 0.0001;\n                particles.rotation.z += 0.0005;\n            }\n            renderer.render(scene, camera);\n        };\n        animate();\n        // Handle resize\n        const handleResize = ()=>{\n            if (!camera || !renderer) return;\n            camera.aspect = window.innerWidth / window.innerHeight;\n            camera.updateProjectionMatrix();\n            renderer.setSize(window.innerWidth, window.innerHeight);\n        };\n        window.addEventListener(\"resize\", handleResize);\n        // Cleanup\n        return ()=>{\n            window.removeEventListener(\"mousemove\", handleMouseMove);\n            window.removeEventListener(\"resize\", handleResize);\n            if (animationIdRef.current) {\n                cancelAnimationFrame(animationIdRef.current);\n            }\n            if (renderer) {\n                renderer.dispose();\n            }\n            if (particles) {\n                particles.geometry.dispose();\n                particles.material.dispose();\n            }\n        };\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n        ref: canvasRef,\n        className: \"fixed inset-0 w-full h-full pointer-events-none z-0\",\n        style: {\n            background: \"transparent\"\n        }\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/effects/ThreeBackground.tsx\",\n        lineNumber: 180,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/effects/ThreeBackground.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/providers/index.tsx":
/*!********************************************!*\
  !*** ./src/components/providers/index.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Providers: () => (/* binding */ Providers)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-query */ \"(ssr)/./node_modules/react-query/es/index.js\");\n/* harmony import */ var react_query_devtools__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-query/devtools */ \"(ssr)/./node_modules/react-query/devtools/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ Providers auto */ \n\n\n\nfunction Providers({ children }) {\n    const [queryClient] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(()=>new react_query__WEBPACK_IMPORTED_MODULE_1__.QueryClient({\n            defaultOptions: {\n                queries: {\n                    staleTime: 5 * 60 * 1000,\n                    cacheTime: 10 * 60 * 1000,\n                    retry: (failureCount, error)=>{\n                        // Don't retry on 4xx errors\n                        if (error?.response?.status >= 400 && error?.response?.status < 500) {\n                            return false;\n                        }\n                        return failureCount < 3;\n                    }\n                },\n                mutations: {\n                    retry: false\n                }\n            }\n        }));\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_query__WEBPACK_IMPORTED_MODULE_1__.QueryClientProvider, {\n        client: queryClient,\n        children: [\n            children,\n             true && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_query_devtools__WEBPACK_IMPORTED_MODULE_2__.ReactQueryDevtools, {\n                initialIsOpen: false\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/providers/index.tsx\",\n                lineNumber: 34,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/providers/index.tsx\",\n        lineNumber: 31,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/providers/index.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"e848f83e28e4\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQHBvcnRmb2xpby9jbGllbnQvLi9zcmMvYXBwL2dsb2JhbHMuY3NzPzgzMGIiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCJlODQ4ZjgzZTI4ZTRcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\",\"display\":\"swap\"}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-inter\\\",\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Fira_Code_arguments_subsets_latin_variable_font_fira_code_display_swap_variableName_firaCode___WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Fira_Code\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-fira-code\",\"display\":\"swap\"}],\"variableName\":\"firaCode\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Fira_Code\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-fira-code\\\",\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"firaCode\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Fira_Code_arguments_subsets_latin_variable_font_fira_code_display_swap_variableName_firaCode___WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Fira_Code_arguments_subsets_latin_variable_font_fira_code_display_swap_variableName_firaCode___WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Orbitron_arguments_subsets_latin_variable_font_orbitron_display_swap_variableName_orbitron___WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Orbitron\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-orbitron\",\"display\":\"swap\"}],\"variableName\":\"orbitron\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Orbitron\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-orbitron\\\",\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"orbitron\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Orbitron_arguments_subsets_latin_variable_font_orbitron_display_swap_variableName_orbitron___WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Orbitron_arguments_subsets_latin_variable_font_orbitron_display_swap_variableName_orbitron___WEBPACK_IMPORTED_MODULE_10__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_providers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/providers */ \"(rsc)/./src/components/providers/index.tsx\");\n/* harmony import */ var _components_effects_ThreeBackground__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/effects/ThreeBackground */ \"(rsc)/./src/components/effects/ThreeBackground.tsx\");\n/* harmony import */ var _components_effects_ScanlineOverlay__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/effects/ScanlineOverlay */ \"(rsc)/./src/components/effects/ScanlineOverlay.tsx\");\n/* harmony import */ var _components_effects_CRTFlicker__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/effects/CRTFlicker */ \"(rsc)/./src/components/effects/CRTFlicker.tsx\");\n/* harmony import */ var _components_Analytics__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/Analytics */ \"(rsc)/./src/components/Analytics.tsx\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react-hot-toast */ \"(rsc)/./node_modules/react-hot-toast/dist/index.mjs\");\n\n\n\n\n\n\n\n\n\n\n\nconst metadata = {\n    title: {\n        default: \"Sahil Ali - Full Stack Developer & Cybersecurity Expert\",\n        template: \"%s | Sahil Ali\"\n    },\n    description: \"Full Stack Developer, Competitive Coder, and Cybersecurity Expert. Ex-Microsoft MLSA specializing in AI/ML, Web Development, and Embedded Systems.\",\n    keywords: [\n        \"Sahil Ali\",\n        \"thestarsahil\",\n        \"Full Stack Developer\",\n        \"Cybersecurity\",\n        \"AI/ML\",\n        \"React\",\n        \"Node.js\",\n        \"Python\",\n        \"C++\",\n        \"Embedded Systems\",\n        \"Microsoft MLSA\",\n        \"Competitive Programming\"\n    ],\n    authors: [\n        {\n            name: \"Sahil Ali\",\n            url: \"https://thestarsahil.me\"\n        }\n    ],\n    creator: \"Sahil Ali\",\n    publisher: \"Sahil Ali\",\n    formatDetection: {\n        email: false,\n        address: false,\n        telephone: false\n    },\n    metadataBase: new URL(\"http://localhost:3000\" || 0),\n    alternates: {\n        canonical: \"/\"\n    },\n    openGraph: {\n        type: \"website\",\n        locale: \"en_US\",\n        url: \"/\",\n        title: \"Sahil Ali - Full Stack Developer & Cybersecurity Expert\",\n        description: \"Full Stack Developer, Competitive Coder, and Cybersecurity Expert. Ex-Microsoft MLSA specializing in AI/ML, Web Development, and Embedded Systems.\",\n        siteName: \"Sahil Ali Portfolio\",\n        images: [\n            {\n                url: \"/og-image.jpg\",\n                width: 1200,\n                height: 630,\n                alt: \"Sahil Ali - Portfolio\"\n            }\n        ]\n    },\n    twitter: {\n        card: \"summary_large_image\",\n        title: \"Sahil Ali - Full Stack Developer & Cybersecurity Expert\",\n        description: \"Full Stack Developer, Competitive Coder, and Cybersecurity Expert.\",\n        images: [\n            \"/og-image.jpg\"\n        ],\n        creator: \"@thestarsahil\"\n    },\n    robots: {\n        index: true,\n        follow: true,\n        googleBot: {\n            index: true,\n            follow: true,\n            \"max-video-preview\": -1,\n            \"max-image-preview\": \"large\",\n            \"max-snippet\": -1\n        }\n    },\n    verification: {\n        google: process.env.NEXT_PUBLIC_GOOGLE_VERIFICATION\n    },\n    category: \"technology\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: \"dark\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        type: \"image/svg+xml\",\n                        href: \"/favicon.svg\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/app/layout.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        type: \"image/png\",\n                        href: \"/favicon.png\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/app/layout.tsx\",\n                        lineNumber: 110,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"apple-touch-icon\",\n                        href: \"/apple-touch-icon.png\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/app/layout.tsx\",\n                        lineNumber: 111,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"manifest\",\n                        href: \"/manifest.json\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/app/layout.tsx\",\n                        lineNumber: 112,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"theme-color\",\n                        content: \"#00ffff\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/app/layout.tsx\",\n                        lineNumber: 113,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"color-scheme\",\n                        content: \"dark\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/app/layout.tsx\",\n                        lineNumber: 114,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1, viewport-fit=cover\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/app/layout.tsx\",\n                        lineNumber: 115,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/app/layout.tsx\",\n                lineNumber: 108,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_8___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Fira_Code_arguments_subsets_latin_variable_font_fira_code_display_swap_variableName_firaCode___WEBPACK_IMPORTED_MODULE_9___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Orbitron_arguments_subsets_latin_variable_font_orbitron_display_swap_variableName_orbitron___WEBPACK_IMPORTED_MODULE_10___default().variable)} antialiased`,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_providers__WEBPACK_IMPORTED_MODULE_2__.Providers, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_effects_ThreeBackground__WEBPACK_IMPORTED_MODULE_3__.ThreeBackground, {}, void 0, false, {\n                            fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/app/layout.tsx\",\n                            lineNumber: 120,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_effects_ScanlineOverlay__WEBPACK_IMPORTED_MODULE_4__.ScanlineOverlay, {}, void 0, false, {\n                            fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/app/layout.tsx\",\n                            lineNumber: 121,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_effects_CRTFlicker__WEBPACK_IMPORTED_MODULE_5__.CRTFlicker, {}, void 0, false, {\n                            fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/app/layout.tsx\",\n                            lineNumber: 122,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                            className: \"relative z-10\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/app/layout.tsx\",\n                            lineNumber: 125,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_7__.Toaster, {\n                            position: \"bottom-right\",\n                            toastOptions: {\n                                duration: 4000,\n                                style: {\n                                    background: \"#1a1a1a\",\n                                    color: \"#00ffff\",\n                                    border: \"1px solid #333333\",\n                                    fontFamily: \"var(--font-fira-code)\",\n                                    fontSize: \"14px\"\n                                },\n                                success: {\n                                    iconTheme: {\n                                        primary: \"#00ff00\",\n                                        secondary: \"#1a1a1a\"\n                                    }\n                                },\n                                error: {\n                                    iconTheme: {\n                                        primary: \"#ff0000\",\n                                        secondary: \"#1a1a1a\"\n                                    }\n                                }\n                            }\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/app/layout.tsx\",\n                            lineNumber: 130,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Analytics__WEBPACK_IMPORTED_MODULE_6__.Analytics, {}, void 0, false, {\n                            fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/app/layout.tsx\",\n                            lineNumber: 157,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/app/layout.tsx\",\n                    lineNumber: 118,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/app/layout.tsx\",\n                lineNumber: 117,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/app/layout.tsx\",\n        lineNumber: 107,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/Analytics.tsx":
/*!**************************************!*\
  !*** ./src/components/Analytics.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Analytics: () => (/* binding */ e0),
/* harmony export */   trackContactForm: () => (/* binding */ e3),
/* harmony export */   trackEvent: () => (/* binding */ e1),
/* harmony export */   trackPageView: () => (/* binding */ e2),
/* harmony export */   trackProjectView: () => (/* binding */ e4),
/* harmony export */   trackResumeDownload: () => (/* binding */ e5),
/* harmony export */   trackSocialClick: () => (/* binding */ e6)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/home/<USER>/Documents/GitHub/Portfolio/client/src/components/Analytics.tsx#Analytics`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/home/<USER>/Documents/GitHub/Portfolio/client/src/components/Analytics.tsx#trackEvent`);

const e2 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/home/<USER>/Documents/GitHub/Portfolio/client/src/components/Analytics.tsx#trackPageView`);

const e3 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/home/<USER>/Documents/GitHub/Portfolio/client/src/components/Analytics.tsx#trackContactForm`);

const e4 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/home/<USER>/Documents/GitHub/Portfolio/client/src/components/Analytics.tsx#trackProjectView`);

const e5 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/home/<USER>/Documents/GitHub/Portfolio/client/src/components/Analytics.tsx#trackResumeDownload`);

const e6 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/home/<USER>/Documents/GitHub/Portfolio/client/src/components/Analytics.tsx#trackSocialClick`);


/***/ }),

/***/ "(rsc)/./src/components/effects/CRTFlicker.tsx":
/*!***********************************************!*\
  !*** ./src/components/effects/CRTFlicker.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   CRTFlicker: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/home/<USER>/Documents/GitHub/Portfolio/client/src/components/effects/CRTFlicker.tsx#CRTFlicker`);


/***/ }),

/***/ "(rsc)/./src/components/effects/ScanlineOverlay.tsx":
/*!****************************************************!*\
  !*** ./src/components/effects/ScanlineOverlay.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ScanlineOverlay: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/home/<USER>/Documents/GitHub/Portfolio/client/src/components/effects/ScanlineOverlay.tsx#ScanlineOverlay`);


/***/ }),

/***/ "(rsc)/./src/components/effects/ThreeBackground.tsx":
/*!****************************************************!*\
  !*** ./src/components/effects/ThreeBackground.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ThreeBackground: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/home/<USER>/Documents/GitHub/Portfolio/client/src/components/effects/ThreeBackground.tsx#ThreeBackground`);


/***/ }),

/***/ "(rsc)/./src/components/providers/index.tsx":
/*!********************************************!*\
  !*** ./src/components/providers/index.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Providers: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/home/<USER>/Documents/GitHub/Portfolio/client/src/components/providers/index.tsx#Providers`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/three","vendor-chunks/react-query","vendor-chunks/react-hot-toast","vendor-chunks/match-sorter","vendor-chunks/remove-accents","vendor-chunks/goober","vendor-chunks/@babel"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();