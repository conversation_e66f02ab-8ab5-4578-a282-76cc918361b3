/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\")), \"/home/<USER>/Documents/GitHub/Portfolio/client/src/app/page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"/home/<USER>/Documents/GitHub/Portfolio/client/src/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/home/<USER>/Documents/GitHub/Portfolio/client/src/app/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Fira_Code%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-fira-code%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22firaCode%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Orbitron%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-orbitron%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22orbitron%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fnode_modules%2Freact-hot-toast%2Fdist%2Findex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fsrc%2Fcomponents%2FAnalytics.tsx%22%2C%22ids%22%3A%5B%22Analytics%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fsrc%2Fcomponents%2Feffects%2FCRTFlicker.tsx%22%2C%22ids%22%3A%5B%22CRTFlicker%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fsrc%2Fcomponents%2Feffects%2FScanlineOverlay.tsx%22%2C%22ids%22%3A%5B%22ScanlineOverlay%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fsrc%2Fcomponents%2Feffects%2FThreeBackground.tsx%22%2C%22ids%22%3A%5B%22ThreeBackground%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fsrc%2Fcomponents%2Fproviders%2Findex.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Fira_Code%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-fira-code%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22firaCode%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Orbitron%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-orbitron%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22orbitron%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fnode_modules%2Freact-hot-toast%2Fdist%2Findex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fsrc%2Fcomponents%2FAnalytics.tsx%22%2C%22ids%22%3A%5B%22Analytics%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fsrc%2Fcomponents%2Feffects%2FCRTFlicker.tsx%22%2C%22ids%22%3A%5B%22CRTFlicker%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fsrc%2Fcomponents%2Feffects%2FScanlineOverlay.tsx%22%2C%22ids%22%3A%5B%22ScanlineOverlay%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fsrc%2Fcomponents%2Feffects%2FThreeBackground.tsx%22%2C%22ids%22%3A%5B%22ThreeBackground%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fsrc%2Fcomponents%2Fproviders%2Findex.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/react-hot-toast/dist/index.mjs */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Analytics.tsx */ \"(ssr)/./src/components/Analytics.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/effects/CRTFlicker.tsx */ \"(ssr)/./src/components/effects/CRTFlicker.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/effects/ScanlineOverlay.tsx */ \"(ssr)/./src/components/effects/ScanlineOverlay.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/effects/ThreeBackground.tsx */ \"(ssr)/./src/components/effects/ThreeBackground.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers/index.tsx */ \"(ssr)/./src/components/providers/index.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Fira_Code%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-fira-code%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22firaCode%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Orbitron%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-orbitron%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22orbitron%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fnode_modules%2Freact-hot-toast%2Fdist%2Findex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fsrc%2Fcomponents%2FAnalytics.tsx%22%2C%22ids%22%3A%5B%22Analytics%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fsrc%2Fcomponents%2Feffects%2FCRTFlicker.tsx%22%2C%22ids%22%3A%5B%22CRTFlicker%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fsrc%2Fcomponents%2Feffects%2FScanlineOverlay.tsx%22%2C%22ids%22%3A%5B%22ScanlineOverlay%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fsrc%2Fcomponents%2Feffects%2FThreeBackground.tsx%22%2C%22ids%22%3A%5B%22ThreeBackground%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fsrc%2Fcomponents%2Fproviders%2Findex.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fsrc%2Fcomponents%2Flayout%2FFooter.tsx%22%2C%22ids%22%3A%5B%22Footer%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fsrc%2Fcomponents%2Flayout%2FNavbar.tsx%22%2C%22ids%22%3A%5B%22Navbar%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fsrc%2Fcomponents%2Fsections%2FAbout.tsx%22%2C%22ids%22%3A%5B%22About%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fsrc%2Fcomponents%2Fsections%2FContact.tsx%22%2C%22ids%22%3A%5B%22Contact%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fsrc%2Fcomponents%2Fsections%2FHero.tsx%22%2C%22ids%22%3A%5B%22Hero%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fsrc%2Fcomponents%2Fsections%2FProjects.tsx%22%2C%22ids%22%3A%5B%22Projects%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fsrc%2Fcomponents%2Fsections%2FServices.tsx%22%2C%22ids%22%3A%5B%22Services%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fsrc%2Fcomponents%2Fsections%2FSkills.tsx%22%2C%22ids%22%3A%5B%22Skills%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fsrc%2Fcomponents%2Fui%2FScrollToTop.tsx%22%2C%22ids%22%3A%5B%22ScrollToTop%22%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fsrc%2Fcomponents%2Flayout%2FFooter.tsx%22%2C%22ids%22%3A%5B%22Footer%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fsrc%2Fcomponents%2Flayout%2FNavbar.tsx%22%2C%22ids%22%3A%5B%22Navbar%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fsrc%2Fcomponents%2Fsections%2FAbout.tsx%22%2C%22ids%22%3A%5B%22About%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fsrc%2Fcomponents%2Fsections%2FContact.tsx%22%2C%22ids%22%3A%5B%22Contact%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fsrc%2Fcomponents%2Fsections%2FHero.tsx%22%2C%22ids%22%3A%5B%22Hero%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fsrc%2Fcomponents%2Fsections%2FProjects.tsx%22%2C%22ids%22%3A%5B%22Projects%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fsrc%2Fcomponents%2Fsections%2FServices.tsx%22%2C%22ids%22%3A%5B%22Services%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fsrc%2Fcomponents%2Fsections%2FSkills.tsx%22%2C%22ids%22%3A%5B%22Skills%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fsrc%2Fcomponents%2Fui%2FScrollToTop.tsx%22%2C%22ids%22%3A%5B%22ScrollToTop%22%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/layout/Footer.tsx */ \"(ssr)/./src/components/layout/Footer.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/layout/Navbar.tsx */ \"(ssr)/./src/components/layout/Navbar.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/sections/About.tsx */ \"(ssr)/./src/components/sections/About.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/sections/Contact.tsx */ \"(ssr)/./src/components/sections/Contact.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/sections/Hero.tsx */ \"(ssr)/./src/components/sections/Hero.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/sections/Projects.tsx */ \"(ssr)/./src/components/sections/Projects.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/sections/Services.tsx */ \"(ssr)/./src/components/sections/Services.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/sections/Skills.tsx */ \"(ssr)/./src/components/sections/Skills.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ui/ScrollToTop.tsx */ \"(ssr)/./src/components/ui/ScrollToTop.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fsrc%2Fcomponents%2Flayout%2FFooter.tsx%22%2C%22ids%22%3A%5B%22Footer%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fsrc%2Fcomponents%2Flayout%2FNavbar.tsx%22%2C%22ids%22%3A%5B%22Navbar%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fsrc%2Fcomponents%2Fsections%2FAbout.tsx%22%2C%22ids%22%3A%5B%22About%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fsrc%2Fcomponents%2Fsections%2FContact.tsx%22%2C%22ids%22%3A%5B%22Contact%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fsrc%2Fcomponents%2Fsections%2FHero.tsx%22%2C%22ids%22%3A%5B%22Hero%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fsrc%2Fcomponents%2Fsections%2FProjects.tsx%22%2C%22ids%22%3A%5B%22Projects%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fsrc%2Fcomponents%2Fsections%2FServices.tsx%22%2C%22ids%22%3A%5B%22Services%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fsrc%2Fcomponents%2Fsections%2FSkills.tsx%22%2C%22ids%22%3A%5B%22Skills%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fsrc%2Fcomponents%2Fui%2FScrollToTop.tsx%22%2C%22ids%22%3A%5B%22ScrollToTop%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/components/Analytics.tsx":
/*!**************************************!*\
  !*** ./src/components/Analytics.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Analytics: () => (/* binding */ Analytics),\n/* harmony export */   trackContactForm: () => (/* binding */ trackContactForm),\n/* harmony export */   trackEvent: () => (/* binding */ trackEvent),\n/* harmony export */   trackPageView: () => (/* binding */ trackPageView),\n/* harmony export */   trackProjectView: () => (/* binding */ trackProjectView),\n/* harmony export */   trackResumeDownload: () => (/* binding */ trackResumeDownload),\n/* harmony export */   trackSocialClick: () => (/* binding */ trackSocialClick)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ Analytics,trackEvent,trackPageView,trackContactForm,trackProjectView,trackResumeDownload,trackSocialClick auto */ \n\nfunction Analytics() {\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.usePathname)();\n    const GA_TRACKING_ID = \"G-XXXXXXXXXX\";\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (!GA_TRACKING_ID) return;\n        // Load Google Analytics script\n        const script = document.createElement(\"script\");\n        script.src = `https://www.googletagmanager.com/gtag/js?id=${GA_TRACKING_ID}`;\n        script.async = true;\n        document.head.appendChild(script);\n        // Initialize gtag\n        window.gtag = function gtag() {\n            // eslint-disable-next-line prefer-rest-params\n            window.dataLayer = window.dataLayer || [];\n            // eslint-disable-next-line prefer-rest-params\n            window.dataLayer.push(arguments);\n        };\n        window.gtag(\"js\", new Date());\n        window.gtag(\"config\", GA_TRACKING_ID, {\n            page_path: pathname\n        });\n        return ()=>{\n            document.head.removeChild(script);\n        };\n    }, [\n        GA_TRACKING_ID\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (!GA_TRACKING_ID || !window.gtag) return;\n        window.gtag(\"config\", GA_TRACKING_ID, {\n            page_path: pathname\n        });\n    }, [\n        pathname,\n        GA_TRACKING_ID\n    ]);\n    return null;\n}\n// Analytics tracking functions\nconst trackEvent = (action, category, label, value)=>{\n    if (false) {}\n};\nconst trackPageView = (url)=>{\n    if (false) {}\n};\nconst trackContactForm = ()=>{\n    trackEvent(\"submit\", \"contact_form\", \"contact_page\");\n};\nconst trackProjectView = (projectName)=>{\n    trackEvent(\"view\", \"project\", projectName);\n};\nconst trackResumeDownload = ()=>{\n    trackEvent(\"download\", \"resume\", \"header_button\");\n};\nconst trackSocialClick = (platform)=>{\n    trackEvent(\"click\", \"social_media\", platform);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Analytics.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/effects/CRTFlicker.tsx":
/*!***********************************************!*\
  !*** ./src/components/effects/CRTFlicker.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CRTFlicker: () => (/* binding */ CRTFlicker)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ CRTFlicker auto */ \nfunction CRTFlicker() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"crt-flicker\"\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/effects/CRTFlicker.tsx\",\n        lineNumber: 4,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9lZmZlY3RzL0NSVEZsaWNrZXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFFTyxTQUFTQTtJQUNkLHFCQUFPLDhEQUFDQztRQUFJQyxXQUFVOzs7Ozs7QUFDeEIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9AcG9ydGZvbGlvL2NsaWVudC8uL3NyYy9jb21wb25lbnRzL2VmZmVjdHMvQ1JURmxpY2tlci50c3g/YTE1MiJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmV4cG9ydCBmdW5jdGlvbiBDUlRGbGlja2VyKCkge1xuICByZXR1cm4gPGRpdiBjbGFzc05hbWU9XCJjcnQtZmxpY2tlclwiIC8+O1xufVxuIl0sIm5hbWVzIjpbIkNSVEZsaWNrZXIiLCJkaXYiLCJjbGFzc05hbWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/effects/CRTFlicker.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/effects/ScanlineOverlay.tsx":
/*!****************************************************!*\
  !*** ./src/components/effects/ScanlineOverlay.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ScanlineOverlay: () => (/* binding */ ScanlineOverlay)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ ScanlineOverlay auto */ \nfunction ScanlineOverlay() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"scanline-overlay\"\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/effects/ScanlineOverlay.tsx\",\n        lineNumber: 4,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9lZmZlY3RzL1NjYW5saW5lT3ZlcmxheS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUVPLFNBQVNBO0lBQ2QscUJBQU8sOERBQUNDO1FBQUlDLFdBQVU7Ozs7OztBQUN4QiIsInNvdXJjZXMiOlsid2VicGFjazovL0Bwb3J0Zm9saW8vY2xpZW50Ly4vc3JjL2NvbXBvbmVudHMvZWZmZWN0cy9TY2FubGluZU92ZXJsYXkudHN4PzkxMTkiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5leHBvcnQgZnVuY3Rpb24gU2NhbmxpbmVPdmVybGF5KCkge1xuICByZXR1cm4gPGRpdiBjbGFzc05hbWU9XCJzY2FubGluZS1vdmVybGF5XCIgLz47XG59XG4iXSwibmFtZXMiOlsiU2NhbmxpbmVPdmVybGF5IiwiZGl2IiwiY2xhc3NOYW1lIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/effects/ScanlineOverlay.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/effects/ThreeBackground.tsx":
/*!****************************************************!*\
  !*** ./src/components/effects/ThreeBackground.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThreeBackground: () => (/* binding */ ThreeBackground)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var three__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! three */ \"(ssr)/./node_modules/three/build/three.module.js\");\n/* __next_internal_client_entry_do_not_use__ ThreeBackground auto */ \n\n\nfunction ThreeBackground() {\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const sceneRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    const rendererRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    const cameraRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    const particlesRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    const animationIdRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!canvasRef.current) return;\n        // Scene setup\n        const scene = new three__WEBPACK_IMPORTED_MODULE_2__.Scene();\n        sceneRef.current = scene;\n        // Camera setup\n        const camera = new three__WEBPACK_IMPORTED_MODULE_2__.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 2000);\n        camera.position.z = 1000;\n        cameraRef.current = camera;\n        // Renderer setup\n        const renderer = new three__WEBPACK_IMPORTED_MODULE_2__.WebGLRenderer({\n            canvas: canvasRef.current,\n            alpha: true,\n            antialias: true\n        });\n        renderer.setSize(window.innerWidth, window.innerHeight);\n        renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));\n        rendererRef.current = renderer;\n        // Create particle system\n        const particleCount = 5000;\n        const positions = new Float32Array(particleCount * 3);\n        const colors = new Float32Array(particleCount * 3);\n        const velocities = new Float32Array(particleCount * 3);\n        // Initialize particles\n        for(let i = 0; i < particleCount; i++){\n            const i3 = i * 3;\n            // Positions\n            positions[i3] = (Math.random() - 0.5) * 2000;\n            positions[i3 + 1] = (Math.random() - 0.5) * 2000;\n            positions[i3 + 2] = (Math.random() - 0.5) * 2000;\n            // Colors (cyan to purple gradient)\n            const colorChoice = Math.random();\n            if (colorChoice < 0.6) {\n                // Cyan\n                colors[i3] = 0;\n                colors[i3 + 1] = 1;\n                colors[i3 + 2] = 1;\n            } else if (colorChoice < 0.8) {\n                // Purple\n                colors[i3] = 0.43;\n                colors[i3 + 1] = 0.04;\n                colors[i3 + 2] = 0.46;\n            } else {\n                // Green\n                colors[i3] = 0;\n                colors[i3 + 1] = 1;\n                colors[i3 + 2] = 0;\n            }\n            // Velocities\n            velocities[i3] = (Math.random() - 0.5) * 0.5;\n            velocities[i3 + 1] = (Math.random() - 0.5) * 0.5;\n            velocities[i3 + 2] = (Math.random() - 0.5) * 0.5;\n        }\n        const geometry = new three__WEBPACK_IMPORTED_MODULE_2__.BufferGeometry();\n        geometry.setAttribute(\"position\", new three__WEBPACK_IMPORTED_MODULE_2__.BufferAttribute(positions, 3));\n        geometry.setAttribute(\"color\", new three__WEBPACK_IMPORTED_MODULE_2__.BufferAttribute(colors, 3));\n        const material = new three__WEBPACK_IMPORTED_MODULE_2__.PointsMaterial({\n            size: 2,\n            vertexColors: true,\n            transparent: true,\n            opacity: 0.8,\n            blending: three__WEBPACK_IMPORTED_MODULE_2__.AdditiveBlending\n        });\n        const particles = new three__WEBPACK_IMPORTED_MODULE_2__.Points(geometry, material);\n        scene.add(particles);\n        particlesRef.current = particles;\n        // Mouse interaction\n        let mouseX = 0;\n        let mouseY = 0;\n        const handleMouseMove = (event)=>{\n            mouseX = event.clientX / window.innerWidth * 2 - 1;\n            mouseY = -(event.clientY / window.innerHeight) * 2 + 1;\n        };\n        window.addEventListener(\"mousemove\", handleMouseMove);\n        // Animation loop\n        const animate = ()=>{\n            animationIdRef.current = requestAnimationFrame(animate);\n            if (particles && particles.geometry.attributes.position) {\n                const positions = particles.geometry.attributes.position.array;\n                // Update particle positions\n                for(let i = 0; i < particleCount; i++){\n                    const i3 = i * 3;\n                    // Move particles\n                    positions[i3] += velocities[i3];\n                    positions[i3 + 1] += velocities[i3 + 1];\n                    positions[i3 + 2] += velocities[i3 + 2];\n                    // Wrap around screen\n                    if (positions[i3] > 1000) positions[i3] = -1000;\n                    if (positions[i3] < -1000) positions[i3] = 1000;\n                    if (positions[i3 + 1] > 1000) positions[i3 + 1] = -1000;\n                    if (positions[i3 + 1] < -1000) positions[i3 + 1] = 1000;\n                    if (positions[i3 + 2] > 1000) positions[i3 + 2] = -1000;\n                    if (positions[i3 + 2] < -1000) positions[i3 + 2] = 1000;\n                }\n                particles.geometry.attributes.position.needsUpdate = true;\n            }\n            // Rotate particles based on mouse\n            if (particles) {\n                particles.rotation.x += mouseY * 0.0001;\n                particles.rotation.y += mouseX * 0.0001;\n                particles.rotation.z += 0.0005;\n            }\n            renderer.render(scene, camera);\n        };\n        animate();\n        // Handle resize\n        const handleResize = ()=>{\n            if (!camera || !renderer) return;\n            camera.aspect = window.innerWidth / window.innerHeight;\n            camera.updateProjectionMatrix();\n            renderer.setSize(window.innerWidth, window.innerHeight);\n        };\n        window.addEventListener(\"resize\", handleResize);\n        // Cleanup\n        return ()=>{\n            window.removeEventListener(\"mousemove\", handleMouseMove);\n            window.removeEventListener(\"resize\", handleResize);\n            if (animationIdRef.current) {\n                cancelAnimationFrame(animationIdRef.current);\n            }\n            if (renderer) {\n                renderer.dispose();\n            }\n            if (particles) {\n                particles.geometry.dispose();\n                particles.material.dispose();\n            }\n        };\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n        ref: canvasRef,\n        className: \"fixed inset-0 w-full h-full pointer-events-none z-0\",\n        style: {\n            background: \"transparent\"\n        }\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/effects/ThreeBackground.tsx\",\n        lineNumber: 180,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/effects/ThreeBackground.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/Footer.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Footer.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Footer: () => (/* binding */ Footer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_Github_Linkedin_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Github,Linkedin,Youtube!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/github.js\");\n/* harmony import */ var _barrel_optimize_names_Github_Linkedin_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Github,Linkedin,Youtube!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/linkedin.js\");\n/* harmony import */ var _barrel_optimize_names_Github_Linkedin_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Github,Linkedin,Youtube!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/youtube.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* __next_internal_client_entry_do_not_use__ Footer auto */ \n\n\n\nfunction Footer() {\n    const currentYear = new Date().getFullYear();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"bg-darkweb-surface border-t border-darkweb-border\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                src: \"/assets/footer-landscape.jpg\",\n                                alt: \"Dark Web Landscape\",\n                                fill: true,\n                                className: \"object-cover opacity-20\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/layout/Footer.tsx\",\n                                lineNumber: 15,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 bg-gradient-to-t from-darkweb-surface via-darkweb-surface/80 to-transparent\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/layout/Footer.tsx\",\n                                lineNumber: 21,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/layout/Footer.tsx\",\n                        lineNumber: 14,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative container-custom py-16\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.8\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            className: \"text-center space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"glitch-text text-2xl md:text-3xl font-bold text-darkweb-accent\",\n                                    \"data-text\": \"THANK YOU FOR VISITING\",\n                                    children: \"THANK YOU FOR VISITING\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/layout/Footer.tsx\",\n                                    lineNumber: 32,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-darkweb-muted\",\n                                    children: \"Connection Terminated Safely\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/layout/Footer.tsx\",\n                                    lineNumber: 35,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"terminal-line text-darkweb-green\",\n                                    children: \"exit --secure\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/layout/Footer.tsx\",\n                                    lineNumber: 36,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/layout/Footer.tsx\",\n                            lineNumber: 25,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/layout/Footer.tsx\",\n                        lineNumber: 24,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/layout/Footer.tsx\",\n                lineNumber: 13,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container-custom py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col md:flex-row items-center justify-between gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center md:text-left\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-darkweb-muted text-sm\",\n                                children: [\n                                    \"Created with\",\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-darkweb-accent\",\n                                        children: \"Linux Community\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/layout/Footer.tsx\",\n                                        lineNumber: 48,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" |\",\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-darkweb-muted\",\n                                        children: [\n                                            \"\\xa9 \",\n                                            currentYear,\n                                            \" All rights reserved.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/layout/Footer.tsx\",\n                                        lineNumber: 49,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/layout/Footer.tsx\",\n                                lineNumber: 46,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/layout/Footer.tsx\",\n                            lineNumber: 45,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"https://github.com/thestarsahil\",\n                                    target: \"_blank\",\n                                    rel: \"noopener noreferrer\",\n                                    className: \"p-2 text-darkweb-muted hover:text-darkweb-accent transition-colors hover:scale-110 transform\",\n                                    \"aria-label\": \"GitHub\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Github_Linkedin_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/layout/Footer.tsx\",\n                                        lineNumber: 62,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/layout/Footer.tsx\",\n                                    lineNumber: 55,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"https://www.linkedin.com/in/thestarsahil/\",\n                                    target: \"_blank\",\n                                    rel: \"noopener noreferrer\",\n                                    className: \"p-2 text-darkweb-muted hover:text-darkweb-accent transition-colors hover:scale-110 transform\",\n                                    \"aria-label\": \"LinkedIn\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Github_Linkedin_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/layout/Footer.tsx\",\n                                        lineNumber: 71,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/layout/Footer.tsx\",\n                                    lineNumber: 64,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"https://www.youtube.com/@thestarsahil\",\n                                    target: \"_blank\",\n                                    rel: \"noopener noreferrer\",\n                                    className: \"p-2 text-darkweb-muted hover:text-darkweb-accent transition-colors hover:scale-110 transform\",\n                                    \"aria-label\": \"YouTube\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Github_Linkedin_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/layout/Footer.tsx\",\n                                        lineNumber: 80,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/layout/Footer.tsx\",\n                                    lineNumber: 73,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/layout/Footer.tsx\",\n                            lineNumber: 54,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2 text-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                    src: \"/assets/tor-logo.svg\",\n                                    alt: \"Tor Network\",\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/layout/Footer.tsx\",\n                                    lineNumber: 86,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"glitch-text text-darkweb-accent font-mono\",\n                                    \"data-text\": \"sahil3xvt7rjgd5woap4qd.onion\",\n                                    children: \"sahil3xvt7rjgd5woap4qd.onion\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/layout/Footer.tsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/layout/Footer.tsx\",\n                            lineNumber: 85,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/layout/Footer.tsx\",\n                    lineNumber: 43,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/layout/Footer.tsx\",\n                lineNumber: 42,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/layout/Footer.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/Footer.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/Navbar.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Navbar.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Navbar: () => (/* binding */ Navbar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _barrel_optimize_names_Menu_Terminal_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Menu,Terminal,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/terminal.js\");\n/* harmony import */ var _barrel_optimize_names_Menu_Terminal_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Menu,Terminal,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Menu_Terminal_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Menu,Terminal,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* __next_internal_client_entry_do_not_use__ Navbar auto */ \n\n\n\nconst navItems = [\n    {\n        name: \"Home\",\n        href: \"#home\"\n    },\n    {\n        name: \"About\",\n        href: \"#about\"\n    },\n    {\n        name: \"Services\",\n        href: \"#services\"\n    },\n    {\n        name: \"Skills\",\n        href: \"#skills\"\n    },\n    {\n        name: \"Projects\",\n        href: \"#projects\"\n    },\n    {\n        name: \"Contact\",\n        href: \"#contact\"\n    }\n];\nfunction Navbar() {\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isScrolled, setIsScrolled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleScroll = ()=>{\n            setIsScrolled(window.scrollY > 20);\n        };\n        window.addEventListener(\"scroll\", handleScroll);\n        return ()=>window.removeEventListener(\"scroll\", handleScroll);\n    }, []);\n    const handleNavClick = (href)=>{\n        setIsOpen(false);\n        if (href.startsWith(\"#\")) {\n            const element = document.querySelector(href);\n            if (element) {\n                element.scrollIntoView({\n                    behavior: \"smooth\"\n                });\n            }\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: `fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${isScrolled ? \"bg-darkweb-surface/90 backdrop-blur-md border-b border-darkweb-border\" : \"bg-transparent\"}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container-custom\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between h-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            href: \"/\",\n                            className: \"flex items-center space-x-2 text-darkweb-accent font-mono font-bold text-lg hover:text-darkweb-purple transition-colors\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Terminal_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    className: \"w-6 h-6\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/layout/Navbar.tsx\",\n                                    lineNumber: 55,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"glitch-text\",\n                                    \"data-text\": \"Kali@Sahil:~$\",\n                                    children: [\n                                        \"Kali\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-darkweb-purple\",\n                                            children: \"@Sahil\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/layout/Navbar.tsx\",\n                                            lineNumber: 57,\n                                            columnNumber: 19\n                                        }, this),\n                                        \":~$\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/layout/Navbar.tsx\",\n                                    lineNumber: 56,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/layout/Navbar.tsx\",\n                            lineNumber: 51,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden md:flex items-center space-x-8\",\n                            children: navItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handleNavClick(item.href),\n                                    className: \"text-darkweb-text hover:text-darkweb-accent transition-colors font-mono text-sm relative group\",\n                                    children: [\n                                        item.name,\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"absolute -bottom-1 left-0 w-0 h-0.5 bg-darkweb-accent transition-all duration-300 group-hover:w-full\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/layout/Navbar.tsx\",\n                                            lineNumber: 70,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, item.name, true, {\n                                    fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/layout/Navbar.tsx\",\n                                    lineNumber: 64,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/layout/Navbar.tsx\",\n                            lineNumber: 62,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setIsOpen(!isOpen),\n                            className: \"md:hidden p-2 text-darkweb-text hover:text-darkweb-accent transition-colors\",\n                            \"aria-label\": \"Toggle menu\",\n                            children: isOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Terminal_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"w-6 h-6\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/layout/Navbar.tsx\",\n                                lineNumber: 81,\n                                columnNumber: 23\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Terminal_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                className: \"w-6 h-6\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/layout/Navbar.tsx\",\n                                lineNumber: 81,\n                                columnNumber: 51\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/layout/Navbar.tsx\",\n                            lineNumber: 76,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/layout/Navbar.tsx\",\n                    lineNumber: 49,\n                    columnNumber: 9\n                }, this),\n                isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"md:hidden absolute top-full left-0 right-0 bg-darkweb-surface/95 backdrop-blur-md border-b border-darkweb-border\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-4 py-4 space-y-4\",\n                        children: navItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>handleNavClick(item.href),\n                                className: \"block w-full text-left text-darkweb-text hover:text-darkweb-accent transition-colors font-mono text-sm py-2\",\n                                children: item.name\n                            }, item.name, false, {\n                                fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/layout/Navbar.tsx\",\n                                lineNumber: 90,\n                                columnNumber: 17\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/layout/Navbar.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/layout/Navbar.tsx\",\n                    lineNumber: 87,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/layout/Navbar.tsx\",\n            lineNumber: 48,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/layout/Navbar.tsx\",\n        lineNumber: 41,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/Navbar.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/providers/index.tsx":
/*!********************************************!*\
  !*** ./src/components/providers/index.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Providers: () => (/* binding */ Providers)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-query */ \"(ssr)/./node_modules/react-query/es/index.js\");\n/* harmony import */ var react_query_devtools__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-query/devtools */ \"(ssr)/./node_modules/react-query/devtools/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ Providers auto */ \n\n\n\nfunction Providers({ children }) {\n    const [queryClient] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(()=>new react_query__WEBPACK_IMPORTED_MODULE_1__.QueryClient({\n            defaultOptions: {\n                queries: {\n                    staleTime: 5 * 60 * 1000,\n                    cacheTime: 10 * 60 * 1000,\n                    retry: (failureCount, error)=>{\n                        // Don't retry on 4xx errors\n                        if (error?.response?.status >= 400 && error?.response?.status < 500) {\n                            return false;\n                        }\n                        return failureCount < 3;\n                    }\n                },\n                mutations: {\n                    retry: false\n                }\n            }\n        }));\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_query__WEBPACK_IMPORTED_MODULE_1__.QueryClientProvider, {\n        client: queryClient,\n        children: [\n            children,\n             true && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_query_devtools__WEBPACK_IMPORTED_MODULE_2__.ReactQueryDevtools, {\n                initialIsOpen: false\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/providers/index.tsx\",\n                lineNumber: 34,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/providers/index.tsx\",\n        lineNumber: 31,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/providers/index.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/sections/About.tsx":
/*!*******************************************!*\
  !*** ./src/components/sections/About.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   About: () => (/* binding */ About)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Code_Download_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Code,Download,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/code.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Code_Download_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Code,Download,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Code_Download_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Code,Download,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Code_Download_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Code,Download,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* __next_internal_client_entry_do_not_use__ About auto */ \n\n\n\nfunction About() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"about\",\n        className: \"section-padding bg-darkweb-surface/30\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container-custom\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.8\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    className: \"text-center mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl md:text-4xl font-bold text-darkweb-text mb-4\",\n                            children: [\n                                \"About \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-darkweb-accent glitch-text\",\n                                    \"data-text\": \"me\",\n                                    children: \"me\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/About.tsx\",\n                                    lineNumber: 19,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/About.tsx\",\n                            lineNumber: 18,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-24 h-1 bg-darkweb-accent mx-auto\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/About.tsx\",\n                            lineNumber: 21,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/About.tsx\",\n                    lineNumber: 11,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid lg:grid-cols-2 gap-12 items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                x: -50\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                x: 0\n                            },\n                            transition: {\n                                duration: 0.8\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            className: \"relative\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative w-full max-w-md mx-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 bg-darkweb-accent/20 rounded-lg transform rotate-6\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/About.tsx\",\n                                        lineNumber: 34,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative bg-darkweb-card rounded-lg p-4 border border-darkweb-border\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                            src: \"/assets/profile.jpg\",\n                                            alt: \"Sahil Ali\",\n                                            width: 400,\n                                            height: 500,\n                                            className: \"w-full h-auto rounded-lg\",\n                                            priority: true\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/About.tsx\",\n                                            lineNumber: 36,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/About.tsx\",\n                                        lineNumber: 35,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/About.tsx\",\n                                lineNumber: 33,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/About.tsx\",\n                            lineNumber: 26,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                x: 50\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                x: 0\n                            },\n                            transition: {\n                                duration: 0.8\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"terminal-window\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"terminal-header\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"terminal-buttons\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"terminal-button close\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/About.tsx\",\n                                                            lineNumber: 59,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"terminal-button minimize\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/About.tsx\",\n                                                            lineNumber: 60,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"terminal-button maximize\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/About.tsx\",\n                                                            lineNumber: 61,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/About.tsx\",\n                                                    lineNumber: 58,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"terminal-title\",\n                                                    children: \"about.txt\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/About.tsx\",\n                                                    lineNumber: 63,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/About.tsx\",\n                                            lineNumber: 57,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"terminal-body\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-darkweb-text leading-relaxed\",\n                                                        children: [\n                                                            \"I'm Sahil and I'm a\",\n                                                            \" \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-darkweb-accent font-bold typing-animation\",\n                                                                children: \"Full Stack Developer\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/About.tsx\",\n                                                                lineNumber: 69,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/About.tsx\",\n                                                        lineNumber: 67,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2 text-darkweb-green\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Code_Download_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                        className: \"w-4 h-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/About.tsx\",\n                                                                        lineNumber: 76,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"Open Source Programmer\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/About.tsx\",\n                                                                        lineNumber: 77,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/About.tsx\",\n                                                                lineNumber: 75,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2 text-darkweb-purple\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Code_Download_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        className: \"w-4 h-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/About.tsx\",\n                                                                        lineNumber: 80,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"Ex-Microsoft MLSA\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/About.tsx\",\n                                                                        lineNumber: 81,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/About.tsx\",\n                                                                lineNumber: 79,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2 text-darkweb-accent\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Code_Download_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                        className: \"w-4 h-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/About.tsx\",\n                                                                        lineNumber: 84,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"Cybersecurity Enthusiast\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/About.tsx\",\n                                                                        lineNumber: 85,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/About.tsx\",\n                                                                lineNumber: 83,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/About.tsx\",\n                                                        lineNumber: 74,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-darkweb-muted text-sm leading-relaxed\",\n                                                        children: \"Computer Science Engineer with expertise in full-stack development, competitive programming, and cybersecurity. Passionate about creating innovative solutions and contributing to open-source projects.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/About.tsx\",\n                                                        lineNumber: 89,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/About.tsx\",\n                                                lineNumber: 66,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/About.tsx\",\n                                            lineNumber: 65,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/About.tsx\",\n                                    lineNumber: 56,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.a, {\n                                    href: \"/Resume Sahil 2024.pdf\",\n                                    download: true,\n                                    className: \"btn-primary inline-flex items-center gap-2\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Code_Download_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/About.tsx\",\n                                            lineNumber: 105,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Download CV\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/About.tsx\",\n                                    lineNumber: 98,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/About.tsx\",\n                            lineNumber: 49,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/About.tsx\",\n                    lineNumber: 24,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/About.tsx\",\n            lineNumber: 10,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/About.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/sections/About.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/sections/Contact.tsx":
/*!*********************************************!*\
  !*** ./src/components/sections/Contact.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Contact: () => (/* binding */ Contact)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-hook-form */ \"(ssr)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @hookform/resolvers/zod */ \"(ssr)/./node_modules/@hookform/resolvers/zod/dist/zod.mjs\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! zod */ \"(ssr)/./node_modules/zod/v3/types.js\");\n/* harmony import */ var _barrel_optimize_names_Key_Mail_MapPin_Send_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Key,Mail,MapPin,Send,Shield,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Key_Mail_MapPin_Send_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Key,Mail,MapPin,Send,Shield,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_Key_Mail_MapPin_Send_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Key,Mail,MapPin,Send,Shield,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_Key_Mail_MapPin_Send_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Key,Mail,MapPin,Send,Shield,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/key.js\");\n/* harmony import */ var _barrel_optimize_names_Key_Mail_MapPin_Send_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Key,Mail,MapPin,Send,Shield,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Key_Mail_MapPin_Send_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Key,Mail,MapPin,Send,Shield,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ Contact auto */ \n\n\n\n\n\n\n\nconst contactSchema = zod__WEBPACK_IMPORTED_MODULE_4__.object({\n    name: zod__WEBPACK_IMPORTED_MODULE_4__.string().min(2, \"Name must be at least 2 characters\"),\n    email: zod__WEBPACK_IMPORTED_MODULE_4__.string().email(\"Invalid email address\"),\n    subject: zod__WEBPACK_IMPORTED_MODULE_4__.string().min(5, \"Subject must be at least 5 characters\"),\n    message: zod__WEBPACK_IMPORTED_MODULE_4__.string().min(10, \"Message must be at least 10 characters\")\n});\nfunction Contact() {\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { register, handleSubmit, reset, formState: { errors } } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_5__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__.zodResolver)(contactSchema)\n    });\n    const onSubmit = async (data)=>{\n        setIsSubmitting(true);\n        try {\n            // Simulate encryption animation\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].loading(\"Encrypting message...\", {\n                id: \"contact\"\n            });\n            await new Promise((resolve)=>setTimeout(resolve, 1500));\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].loading(\"Transmitting...\", {\n                id: \"contact\"\n            });\n            // TODO: Replace with actual API call\n            await new Promise((resolve)=>setTimeout(resolve, 2000));\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].success(\"Message transmitted securely!\", {\n                id: \"contact\"\n            });\n            reset();\n        } catch (error) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].error(\"Transmission failed. Please try again.\", {\n                id: \"contact\"\n            });\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"contact\",\n        className: \"section-padding bg-darkweb-surface/30\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container-custom\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.8\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    className: \"text-center mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl md:text-4xl font-bold text-darkweb-text mb-4 flex items-center justify-center gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                    src: \"/assets/tor-logo.svg\",\n                                    alt: \"Tor Network\",\n                                    className: \"w-8 h-8\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Contact.tsx\",\n                                    lineNumber: 66,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-darkweb-accent glitch-text\",\n                                    \"data-text\": \"Contact me\",\n                                    children: \"Contact me\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Contact.tsx\",\n                                    lineNumber: 67,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Contact.tsx\",\n                            lineNumber: 65,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-24 h-1 bg-darkweb-accent mx-auto mb-8\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Contact.tsx\",\n                            lineNumber: 71,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-darkweb-card border border-darkweb-border rounded-lg p-6 max-w-md mx-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"glitch-text text-darkweb-accent font-bold mb-2\",\n                                    \"data-text\": \"SECURE COMMUNICATION CHANNEL\",\n                                    children: \"SECURE COMMUNICATION CHANNEL\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Contact.tsx\",\n                                    lineNumber: 74,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-darkweb-muted text-sm\",\n                                    children: \"Encryption: PGP/GPG | Protocol: TOR\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Contact.tsx\",\n                                    lineNumber: 77,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Contact.tsx\",\n                            lineNumber: 73,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Contact.tsx\",\n                    lineNumber: 58,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid lg:grid-cols-2 gap-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                x: -50\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                x: 0\n                            },\n                            transition: {\n                                duration: 0.8\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            className: \"terminal-window\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"terminal-header\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"terminal-buttons\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"terminal-button close\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Contact.tsx\",\n                                                    lineNumber: 92,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"terminal-button minimize\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Contact.tsx\",\n                                                    lineNumber: 93,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"terminal-button maximize\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Contact.tsx\",\n                                                    lineNumber: 94,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Contact.tsx\",\n                                            lineNumber: 91,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"terminal-title glitch-text\",\n                                            \"data-text\": \"kali@sahil: ~/contact\",\n                                            children: \"kali@sahil: ~/contact\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Contact.tsx\",\n                                            lineNumber: 96,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Contact.tsx\",\n                                    lineNumber: 90,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"terminal-body\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"terminal-line\",\n                                            children: \"cat contact_info.txt\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Contact.tsx\",\n                                            lineNumber: 101,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Key_Mail_MapPin_Send_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            className: \"w-5 h-5 text-darkweb-accent\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Contact.tsx\",\n                                                            lineNumber: 104,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-darkweb-muted text-sm\",\n                                                                    children: \"Identity\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Contact.tsx\",\n                                                                    lineNumber: 106,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-darkweb-text font-bold glitch-text\",\n                                                                    \"data-text\": \"Sahil Ali\",\n                                                                    children: \"Sahil Ali\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Contact.tsx\",\n                                                                    lineNumber: 107,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Contact.tsx\",\n                                                            lineNumber: 105,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Contact.tsx\",\n                                                    lineNumber: 103,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Key_Mail_MapPin_Send_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            className: \"w-5 h-5 text-darkweb-accent\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Contact.tsx\",\n                                                            lineNumber: 114,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-darkweb-muted text-sm\",\n                                                                    children: \"Location\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Contact.tsx\",\n                                                                    lineNumber: 116,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-darkweb-text\",\n                                                                    children: \"Anonymous\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Contact.tsx\",\n                                                                    lineNumber: 117,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Contact.tsx\",\n                                                            lineNumber: 115,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Contact.tsx\",\n                                                    lineNumber: 113,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Key_Mail_MapPin_Send_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"w-5 h-5 text-darkweb-accent\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Contact.tsx\",\n                                                            lineNumber: 122,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-darkweb-muted text-sm\",\n                                                                    children: \"Email\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Contact.tsx\",\n                                                                    lineNumber: 124,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-darkweb-text\",\n                                                                    children: \"<EMAIL>\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Contact.tsx\",\n                                                                    lineNumber: 125,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Contact.tsx\",\n                                                            lineNumber: 123,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Contact.tsx\",\n                                                    lineNumber: 121,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Key_Mail_MapPin_Send_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            className: \"w-5 h-5 text-darkweb-accent\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Contact.tsx\",\n                                                            lineNumber: 130,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-darkweb-muted text-sm\",\n                                                                    children: \"PGP Key\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Contact.tsx\",\n                                                                    lineNumber: 132,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-darkweb-text font-mono text-sm\",\n                                                                    children: \"4B1D C371 57E3 2F8A...\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Contact.tsx\",\n                                                                    lineNumber: 133,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Contact.tsx\",\n                                                            lineNumber: 131,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Contact.tsx\",\n                                                    lineNumber: 129,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Contact.tsx\",\n                                            lineNumber: 102,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"terminal-line mt-6\",\n                                            children: \"./verify_identity.sh\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Contact.tsx\",\n                                            lineNumber: 138,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2 text-darkweb-green mt-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Identity Verified\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Contact.tsx\",\n                                                    lineNumber: 140,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Key_Mail_MapPin_Send_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Contact.tsx\",\n                                                    lineNumber: 141,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Contact.tsx\",\n                                            lineNumber: 139,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Contact.tsx\",\n                                    lineNumber: 100,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Contact.tsx\",\n                            lineNumber: 83,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                x: 50\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                x: 0\n                            },\n                            transition: {\n                                duration: 0.8\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            className: \"terminal-window\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"terminal-header\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"terminal-buttons\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"terminal-button close\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Contact.tsx\",\n                                                    lineNumber: 156,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"terminal-button minimize\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Contact.tsx\",\n                                                    lineNumber: 157,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"terminal-button maximize\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Contact.tsx\",\n                                                    lineNumber: 158,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Contact.tsx\",\n                                            lineNumber: 155,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"terminal-title glitch-text\",\n                                            \"data-text\": \"kali@sahil: ~/message\",\n                                            children: \"kali@sahil: ~/message\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Contact.tsx\",\n                                            lineNumber: 160,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Contact.tsx\",\n                                    lineNumber: 154,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"terminal-body\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"terminal-line\",\n                                            children: \"./send_encrypted_message.sh\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Contact.tsx\",\n                                            lineNumber: 165,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                            onSubmit: handleSubmit(onSubmit),\n                                            className: \"space-y-6 mt-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid md:grid-cols-2 gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    ...register(\"name\"),\n                                                                    type: \"text\",\n                                                                    placeholder: \"Identity\",\n                                                                    className: \"input-terminal\",\n                                                                    disabled: isSubmitting\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Contact.tsx\",\n                                                                    lineNumber: 170,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                errors.name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-darkweb-red text-xs mt-1\",\n                                                                    children: errors.name.message\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Contact.tsx\",\n                                                                    lineNumber: 178,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Contact.tsx\",\n                                                            lineNumber: 169,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    ...register(\"email\"),\n                                                                    type: \"email\",\n                                                                    placeholder: \"Secure Email\",\n                                                                    className: \"input-terminal\",\n                                                                    disabled: isSubmitting\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Contact.tsx\",\n                                                                    lineNumber: 183,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                errors.email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-darkweb-red text-xs mt-1\",\n                                                                    children: errors.email.message\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Contact.tsx\",\n                                                                    lineNumber: 191,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Contact.tsx\",\n                                                            lineNumber: 182,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Contact.tsx\",\n                                                    lineNumber: 168,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            ...register(\"subject\"),\n                                                            type: \"text\",\n                                                            placeholder: \"Message Subject\",\n                                                            className: \"input-terminal\",\n                                                            disabled: isSubmitting\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Contact.tsx\",\n                                                            lineNumber: 197,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        errors.subject && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-darkweb-red text-xs mt-1\",\n                                                            children: errors.subject.message\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Contact.tsx\",\n                                                            lineNumber: 205,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Contact.tsx\",\n                                                    lineNumber: 196,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                            ...register(\"message\"),\n                                                            rows: 6,\n                                                            placeholder: \"Encrypted Message...\",\n                                                            className: \"input-terminal resize-none\",\n                                                            disabled: isSubmitting\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Contact.tsx\",\n                                                            lineNumber: 210,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        errors.message && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-darkweb-red text-xs mt-1\",\n                                                            children: errors.message.message\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Contact.tsx\",\n                                                            lineNumber: 218,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Contact.tsx\",\n                                                    lineNumber: 209,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between text-sm\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2 text-darkweb-green\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Key_Mail_MapPin_Send_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                className: \"w-4 h-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Contact.tsx\",\n                                                                lineNumber: 224,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"End-to-End Encryption Active\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Contact.tsx\",\n                                                                lineNumber: 225,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Contact.tsx\",\n                                                        lineNumber: 223,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Contact.tsx\",\n                                                    lineNumber: 222,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"submit\",\n                                                    disabled: isSubmitting,\n                                                    className: \"btn-primary w-full flex items-center justify-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                                    children: [\n                                                        isSubmitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"loading-spinner\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Contact.tsx\",\n                                                            lineNumber: 235,\n                                                            columnNumber: 21\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Key_Mail_MapPin_Send_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Contact.tsx\",\n                                                            lineNumber: 237,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        isSubmitting ? \"Transmitting...\" : \"Transmit Secure Message\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Contact.tsx\",\n                                                    lineNumber: 229,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Contact.tsx\",\n                                            lineNumber: 167,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Contact.tsx\",\n                                    lineNumber: 164,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Contact.tsx\",\n                            lineNumber: 147,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Contact.tsx\",\n                    lineNumber: 81,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Contact.tsx\",\n            lineNumber: 57,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Contact.tsx\",\n        lineNumber: 56,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/sections/Contact.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/sections/Hero.tsx":
/*!******************************************!*\
  !*** ./src/components/sections/Hero.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Hero: () => (/* binding */ Hero)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_Download_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Terminal!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/terminal.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Terminal!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* __next_internal_client_entry_do_not_use__ Hero auto */ \n\n\n\nconst roles = [\n    \"C++ Programmer\",\n    \"Embedded Developer\",\n    \"Full Stack Developer\",\n    \"Cybersecurity Expert\"\n];\nfunction Hero() {\n    const [currentRole, setCurrentRole] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [displayText, setDisplayText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isDeleting, setIsDeleting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const role = roles[currentRole];\n        const timeout = setTimeout(()=>{\n            if (!isDeleting) {\n                if (displayText.length < role.length) {\n                    setDisplayText(role.slice(0, displayText.length + 1));\n                } else {\n                    setTimeout(()=>setIsDeleting(true), 2000);\n                }\n            } else {\n                if (displayText.length > 0) {\n                    setDisplayText(displayText.slice(0, -1));\n                } else {\n                    setIsDeleting(false);\n                    setCurrentRole((prev)=>(prev + 1) % roles.length);\n                }\n            }\n        }, isDeleting ? 50 : 100);\n        return ()=>clearTimeout(timeout);\n    }, [\n        displayText,\n        isDeleting,\n        currentRole\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"home\",\n        className: \"min-h-screen flex items-center justify-center section-padding\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container-custom\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-4xl mx-auto text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.8\n                        },\n                        className: \"terminal-window max-w-3xl mx-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"terminal-header\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"terminal-buttons\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"terminal-button close\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Hero.tsx\",\n                                                lineNumber: 49,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"terminal-button minimize\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Hero.tsx\",\n                                                lineNumber: 50,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"terminal-button maximize\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Hero.tsx\",\n                                                lineNumber: 51,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Hero.tsx\",\n                                        lineNumber: 48,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"terminal-title glitch-text\",\n                                        \"data-text\": \"kali@sahil: ~/portfolio\",\n                                        children: \"kali@sahil: ~/portfolio\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Hero.tsx\",\n                                        lineNumber: 53,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Hero.tsx\",\n                                lineNumber: 47,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"terminal-body text-left space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"terminal-line\",\n                                        children: \"whoami\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Hero.tsx\",\n                                        lineNumber: 60,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                        initial: {\n                                            opacity: 0\n                                        },\n                                        animate: {\n                                            opacity: 1\n                                        },\n                                        transition: {\n                                            delay: 0.5,\n                                            duration: 0.8\n                                        },\n                                        className: \"text-2xl md:text-3xl font-bold text-darkweb-text mb-4\",\n                                        children: \"Hello, I am Sahil Ali\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Hero.tsx\",\n                                        lineNumber: 61,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                        initial: {\n                                            opacity: 0\n                                        },\n                                        animate: {\n                                            opacity: 1\n                                        },\n                                        transition: {\n                                            delay: 1,\n                                            duration: 0.8\n                                        },\n                                        className: \"text-xl md:text-2xl text-darkweb-accent mb-6 glitch-text\",\n                                        \"data-text\": \"Welcome to Dark Reality\",\n                                        children: \"Welcome to Dark Reality\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Hero.tsx\",\n                                        lineNumber: 70,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"terminal-line\",\n                                        children: \"profession\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Hero.tsx\",\n                                        lineNumber: 80,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                        initial: {\n                                            opacity: 0\n                                        },\n                                        animate: {\n                                            opacity: 1\n                                        },\n                                        transition: {\n                                            delay: 1.5,\n                                            duration: 0.8\n                                        },\n                                        className: \"text-lg md:text-xl text-darkweb-text mb-6\",\n                                        children: [\n                                            \"I'm a\",\n                                            \" \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-darkweb-accent font-bold\",\n                                                children: [\n                                                    displayText,\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"animate-blink\",\n                                                        children: \"|\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Hero.tsx\",\n                                                        lineNumber: 90,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Hero.tsx\",\n                                                lineNumber: 88,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Hero.tsx\",\n                                        lineNumber: 81,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"terminal-line\",\n                                        children: \"contact --hire\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Hero.tsx\",\n                                        lineNumber: 94,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 20\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            delay: 2,\n                                            duration: 0.8\n                                        },\n                                        className: \"flex flex-col sm:flex-row gap-4 mt-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>{\n                                                    const element = document.querySelector(\"#contact\");\n                                                    if (element) {\n                                                        element.scrollIntoView({\n                                                            behavior: \"smooth\"\n                                                        });\n                                                    }\n                                                },\n                                                className: \"btn-terminal flex items-center justify-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Hero.tsx\",\n                                                        lineNumber: 110,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Hire me\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Hero.tsx\",\n                                                lineNumber: 101,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"/Resume Sahil 2024.pdf\",\n                                                download: true,\n                                                className: \"btn-secondary flex items-center justify-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Hero.tsx\",\n                                                        lineNumber: 119,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Download CV\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Hero.tsx\",\n                                                lineNumber: 114,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Hero.tsx\",\n                                        lineNumber: 95,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Hero.tsx\",\n                                lineNumber: 59,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Hero.tsx\",\n                        lineNumber: 40,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                        initial: {\n                            opacity: 0\n                        },\n                        animate: {\n                            opacity: 1\n                        },\n                        transition: {\n                            delay: 2.5,\n                            duration: 1\n                        },\n                        className: \"absolute top-1/4 left-10 text-darkweb-green font-mono text-sm opacity-30 animate-float\",\n                        children: \"> Initializing...\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Hero.tsx\",\n                        lineNumber: 127,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                        initial: {\n                            opacity: 0\n                        },\n                        animate: {\n                            opacity: 1\n                        },\n                        transition: {\n                            delay: 3,\n                            duration: 1\n                        },\n                        className: \"absolute top-1/3 right-10 text-darkweb-purple font-mono text-sm opacity-30 animate-float animate-delay-200\",\n                        children: \"[SECURE CONNECTION]\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Hero.tsx\",\n                        lineNumber: 136,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                        initial: {\n                            opacity: 0\n                        },\n                        animate: {\n                            opacity: 1\n                        },\n                        transition: {\n                            delay: 3.5,\n                            duration: 1\n                        },\n                        className: \"absolute bottom-1/4 left-1/4 text-darkweb-accent font-mono text-sm opacity-30 animate-float animate-delay-500\",\n                        children: \"$ sudo access_granted\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Hero.tsx\",\n                        lineNumber: 145,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Hero.tsx\",\n                lineNumber: 39,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Hero.tsx\",\n            lineNumber: 38,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Hero.tsx\",\n        lineNumber: 37,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/sections/Hero.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/sections/Projects.tsx":
/*!**********************************************!*\
  !*** ./src/components/sections/Projects.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Projects: () => (/* binding */ Projects)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_Github_Lock_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,Github,Lock!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/lock.js\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_Github_Lock_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,Github,Lock!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/github.js\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_Github_Lock_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,Github,Lock!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* __next_internal_client_entry_do_not_use__ Projects auto */ \n\n\n\nconst projects = [\n    {\n        title: \"HealthCare Chatbot\",\n        description: \"Building a 24x7 Healthcare Chatbot using NLP 24x7 support to patients\",\n        image: \"/assets/healthcare.jpg\",\n        technologies: [\n            \"NLP\",\n            \"AI\",\n            \"Python\"\n        ],\n        githubUrl: \"https://github.com/thestarsahil/Mentalmate\",\n        liveUrl: null\n    },\n    {\n        title: \"Weather WebApp\",\n        description: \"Web Application using HTML CSS JS with the help of Open Weather API\",\n        image: \"/assets/weather.png\",\n        technologies: [\n            \"HTML\",\n            \"CSS\",\n            \"JS\",\n            \"API\"\n        ],\n        githubUrl: null,\n        liveUrl: null\n    },\n    {\n        title: \"Contoso Real Estate\",\n        description: \"Microsoft Real Estate Project allows users to listed properties for sale or rent\",\n        image: \"/assets/real-estate.jpg\",\n        technologies: [\n            \"C#\",\n            \".NET\",\n            \"Azure\"\n        ],\n        githubUrl: null,\n        liveUrl: null\n    },\n    {\n        title: \"CropForesight\",\n        description: \"Assist farmers making smart choices about which crops to grow on their land\",\n        image: \"/assets/crop.jpg\",\n        technologies: [\n            \"ML\",\n            \"Python\",\n            \"Data\"\n        ],\n        githubUrl: null,\n        liveUrl: null\n    },\n    {\n        title: \"Book Finder\",\n        description: \"Real-time project that helps users find and purchase books online FREE\",\n        image: \"/assets/book-finder.jpg\",\n        technologies: [\n            \"React\",\n            \"Node\",\n            \"MongoDB\"\n        ],\n        githubUrl: null,\n        liveUrl: null\n    }\n];\nfunction Projects() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"projects\",\n        className: \"section-padding\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container-custom\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.8\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    className: \"text-center mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl md:text-4xl font-bold text-darkweb-text mb-4 flex items-center justify-center gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                    src: \"/assets/tor-logo.svg\",\n                                    alt: \"Tor Network\",\n                                    className: \"w-8 h-8\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Projects.tsx\",\n                                    lineNumber: 62,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-darkweb-accent glitch-text\",\n                                    \"data-text\": \"My Projects\",\n                                    children: \"My Projects\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Projects.tsx\",\n                                    lineNumber: 63,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Projects.tsx\",\n                            lineNumber: 61,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-24 h-1 bg-darkweb-accent mx-auto mb-8\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Projects.tsx\",\n                            lineNumber: 67,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-darkweb-card border border-darkweb-border rounded-lg p-6 max-w-md mx-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"glitch-text text-darkweb-accent font-bold mb-2\",\n                                    \"data-text\": \"PROJECTS\",\n                                    children: \"PROJECTS\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Projects.tsx\",\n                                    lineNumber: 70,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-darkweb-muted text-sm\",\n                                    children: \"Access Level: Restricted\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Projects.tsx\",\n                                    lineNumber: 73,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Projects.tsx\",\n                            lineNumber: 69,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Projects.tsx\",\n                    lineNumber: 54,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                    children: projects.map((project, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.8,\n                                delay: index * 0.1\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            className: \"project-card\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative overflow-hidden rounded-t-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                            src: project.image,\n                                            alt: project.title,\n                                            width: 400,\n                                            height: 250,\n                                            className: \"project-image\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Projects.tsx\",\n                                            lineNumber: 88,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"project-overlay\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Github_Lock_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                className: \"w-8 h-8 text-darkweb-accent\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Projects.tsx\",\n                                                lineNumber: 96,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Projects.tsx\",\n                                            lineNumber: 95,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Projects.tsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6 space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-xl font-bold text-darkweb-text group-hover:text-darkweb-accent transition-colors\",\n                                                    children: project.title\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Projects.tsx\",\n                                                    lineNumber: 102,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-2\",\n                                                    children: [\n                                                        project.githubUrl && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: project.githubUrl,\n                                                            target: \"_blank\",\n                                                            rel: \"noopener noreferrer\",\n                                                            className: \"p-2 text-darkweb-muted hover:text-darkweb-accent transition-colors\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Github_Lock_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                className: \"w-4 h-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Projects.tsx\",\n                                                                lineNumber: 113,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Projects.tsx\",\n                                                            lineNumber: 107,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        project.liveUrl && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: project.liveUrl,\n                                                            target: \"_blank\",\n                                                            rel: \"noopener noreferrer\",\n                                                            className: \"p-2 text-darkweb-muted hover:text-darkweb-accent transition-colors\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Github_Lock_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                className: \"w-4 h-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Projects.tsx\",\n                                                                lineNumber: 123,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Projects.tsx\",\n                                                            lineNumber: 117,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Projects.tsx\",\n                                                    lineNumber: 105,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Projects.tsx\",\n                                            lineNumber: 101,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-darkweb-muted text-sm leading-relaxed\",\n                                            children: project.description\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Projects.tsx\",\n                                            lineNumber: 129,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap gap-2\",\n                                            children: project.technologies.map((tech)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"tech-tag\",\n                                                    children: tech\n                                                }, tech, false, {\n                                                    fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Projects.tsx\",\n                                                    lineNumber: 135,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Projects.tsx\",\n                                            lineNumber: 133,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Projects.tsx\",\n                                    lineNumber: 100,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, project.title, true, {\n                            fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Projects.tsx\",\n                            lineNumber: 79,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Projects.tsx\",\n                    lineNumber: 77,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Projects.tsx\",\n            lineNumber: 53,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Projects.tsx\",\n        lineNumber: 52,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/sections/Projects.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/sections/Services.tsx":
/*!**********************************************!*\
  !*** ./src/components/sections/Services.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Services: () => (/* binding */ Services)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_Award_Bot_Code_lucide_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Bot,Code!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Bot_Code_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Bot,Code!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Bot_Code_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Bot,Code!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/code.js\");\n/* __next_internal_client_entry_do_not_use__ Services auto */ \n\n\nconst services = [\n    {\n        icon: _barrel_optimize_names_Award_Bot_Code_lucide_react__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n        title: \"Fastest Coder by Microsoft\",\n        description: \"Build a Finance App with JS and Github Copilot\",\n        color: \"text-blue-400\"\n    },\n    {\n        icon: _barrel_optimize_names_Award_Bot_Code_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n        title: \"AI/ML Contributor\",\n        description: \"Contributing to various projects related to AI, React, Cloud\",\n        color: \"text-darkweb-accent\"\n    },\n    {\n        icon: _barrel_optimize_names_Award_Bot_Code_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        title: \"NLP Model Contributor\",\n        description: \"Contributing to various projects related to AI, React, Cloud\",\n        color: \"text-darkweb-purple\"\n    }\n];\nfunction Services() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"services\",\n        className: \"section-padding\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container-custom\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.8\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    className: \"text-center mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl md:text-4xl font-bold text-darkweb-text mb-4\",\n                            children: [\n                                \"Hackathon and\",\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-darkweb-accent glitch-text\",\n                                    \"data-text\": \"Open Source\",\n                                    children: \"Open Source\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Services.tsx\",\n                                    lineNumber: 40,\n                                    columnNumber: 13\n                                }, this),\n                                \" \",\n                                \"Contributor\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Services.tsx\",\n                            lineNumber: 38,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-24 h-1 bg-darkweb-accent mx-auto\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Services.tsx\",\n                            lineNumber: 45,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Services.tsx\",\n                    lineNumber: 31,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                    children: services.map((service, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.8,\n                                delay: index * 0.2\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            className: \"card-glow group\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `inline-flex p-4 rounded-full bg-darkweb-surface ${service.color} group-hover:scale-110 transition-transform duration-300`,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(service.icon, {\n                                            className: \"w-8 h-8\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Services.tsx\",\n                                            lineNumber: 60,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Services.tsx\",\n                                        lineNumber: 59,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-bold text-darkweb-text group-hover:text-darkweb-accent transition-colors\",\n                                        children: service.title\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Services.tsx\",\n                                        lineNumber: 63,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-darkweb-muted leading-relaxed\",\n                                        children: service.description\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Services.tsx\",\n                                        lineNumber: 67,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Services.tsx\",\n                                lineNumber: 58,\n                                columnNumber: 15\n                            }, this)\n                        }, service.title, false, {\n                            fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Services.tsx\",\n                            lineNumber: 50,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Services.tsx\",\n                    lineNumber: 48,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Services.tsx\",\n            lineNumber: 30,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Services.tsx\",\n        lineNumber: 29,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/sections/Services.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/sections/Skills.tsx":
/*!********************************************!*\
  !*** ./src/components/sections/Skills.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Skills: () => (/* binding */ Skills)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_Bot_Code_Cpu_Database_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Code,Cpu,Database,Terminal!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/terminal.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Code_Cpu_Database_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Code,Cpu,Database,Terminal!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Code_Cpu_Database_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Code,Cpu,Database,Terminal!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/code.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Code_Cpu_Database_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Code,Cpu,Database,Terminal!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Code_Cpu_Database_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Code,Cpu,Database,Terminal!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/cpu.js\");\n/* __next_internal_client_entry_do_not_use__ Skills auto */ \n\n\nconst skills = [\n    {\n        name: \"Prompt Engineering\",\n        level: 62,\n        icon: _barrel_optimize_names_Bot_Code_Cpu_Database_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_1__[\"default\"]\n    },\n    {\n        name: \"AI Model\",\n        level: 31,\n        icon: _barrel_optimize_names_Bot_Code_Cpu_Database_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"]\n    },\n    {\n        name: \"Embedding C++\",\n        level: 65,\n        icon: _barrel_optimize_names_Bot_Code_Cpu_Database_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n    },\n    {\n        name: \"Server and Database\",\n        level: 62,\n        icon: _barrel_optimize_names_Bot_Code_Cpu_Database_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n    },\n    {\n        name: \"Data Structure and Algorithm\",\n        level: 53,\n        icon: _barrel_optimize_names_Bot_Code_Cpu_Database_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n    }\n];\nfunction Skills() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"skills\",\n        className: \"section-padding bg-darkweb-surface/30\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container-custom\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.8\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    className: \"text-center mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl md:text-4xl font-bold text-darkweb-text mb-4 flex items-center justify-center gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                    src: \"/assets/tor-logo.svg\",\n                                    alt: \"Tor Network\",\n                                    className: \"w-8 h-8\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Skills.tsx\",\n                                    lineNumber: 26,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-darkweb-accent glitch-text\",\n                                    \"data-text\": \"My skills\",\n                                    children: \"My skills\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Skills.tsx\",\n                                    lineNumber: 27,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Skills.tsx\",\n                            lineNumber: 25,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-24 h-1 bg-darkweb-accent mx-auto\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Skills.tsx\",\n                            lineNumber: 31,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Skills.tsx\",\n                    lineNumber: 18,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid lg:grid-cols-2 gap-12 items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                x: -50\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                x: 0\n                            },\n                            transition: {\n                                duration: 0.8\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            className: \"terminal-window\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"terminal-header\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"terminal-buttons\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"terminal-button close\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Skills.tsx\",\n                                                    lineNumber: 45,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"terminal-button minimize\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Skills.tsx\",\n                                                    lineNumber: 46,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"terminal-button maximize\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Skills.tsx\",\n                                                    lineNumber: 47,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Skills.tsx\",\n                                            lineNumber: 44,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"terminal-title glitch-text\",\n                                            \"data-text\": \"kali@sahil: ~/skills\",\n                                            children: \"kali@sahil: ~/skills\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Skills.tsx\",\n                                            lineNumber: 49,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Skills.tsx\",\n                                    lineNumber: 43,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"terminal-body\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"terminal-line\",\n                                            children: \"cat skills.txt\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Skills.tsx\",\n                                            lineNumber: 54,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-darkweb-accent font-bold mb-4 glitch-text\",\n                                            \"data-text\": \"Dark Web Skills & Expertise\",\n                                            children: \"Dark Web Skills & Expertise\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Skills.tsx\",\n                                            lineNumber: 55,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-darkweb-text mb-4 typing-animation\",\n                                            children: \"Computer Science Engineer with Trails operations\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Skills.tsx\",\n                                            lineNumber: 58,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"terminal-line\",\n                                            children: \"./show_credentials.sh\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Skills.tsx\",\n                                            lineNumber: 61,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"btn-terminal mt-4\",\n                                            children: \"View Credentials\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Skills.tsx\",\n                                            lineNumber: 62,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Skills.tsx\",\n                                    lineNumber: 53,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Skills.tsx\",\n                            lineNumber: 36,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                x: 50\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                x: 0\n                            },\n                            transition: {\n                                duration: 0.8\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            className: \"space-y-6\",\n                            children: skills.map((skill, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    whileInView: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.8,\n                                        delay: index * 0.1\n                                    },\n                                    viewport: {\n                                        once: true\n                                    },\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(skill.icon, {\n                                                            className: \"w-4 h-4 text-darkweb-accent\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Skills.tsx\",\n                                                            lineNumber: 87,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-darkweb-text font-mono text-sm\",\n                                                            children: skill.name\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Skills.tsx\",\n                                                            lineNumber: 88,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Skills.tsx\",\n                                                    lineNumber: 86,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-darkweb-accent font-mono text-sm\",\n                                                    children: [\n                                                        skill.level,\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Skills.tsx\",\n                                                    lineNumber: 92,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Skills.tsx\",\n                                            lineNumber: 85,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"skill-bar\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                                className: \"skill-progress\",\n                                                initial: {\n                                                    width: 0\n                                                },\n                                                whileInView: {\n                                                    width: `${skill.level}%`\n                                                },\n                                                transition: {\n                                                    duration: 1.5,\n                                                    delay: index * 0.1\n                                                },\n                                                viewport: {\n                                                    once: true\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Skills.tsx\",\n                                                lineNumber: 98,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Skills.tsx\",\n                                            lineNumber: 97,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, skill.name, true, {\n                                    fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Skills.tsx\",\n                                    lineNumber: 77,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Skills.tsx\",\n                            lineNumber: 69,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Skills.tsx\",\n                    lineNumber: 34,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Skills.tsx\",\n            lineNumber: 17,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Skills.tsx\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/sections/Skills.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/ScrollToTop.tsx":
/*!*******************************************!*\
  !*** ./src/components/ui/ScrollToTop.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ScrollToTop: () => (/* binding */ ScrollToTop)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-up.js\");\n/* __next_internal_client_entry_do_not_use__ ScrollToTop auto */ \n\n\nfunction ScrollToTop() {\n    const [isVisible, setIsVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const toggleVisibility = ()=>{\n            if (window.pageYOffset > 500) {\n                setIsVisible(true);\n            } else {\n                setIsVisible(false);\n            }\n        };\n        window.addEventListener(\"scroll\", toggleVisibility);\n        return ()=>window.removeEventListener(\"scroll\", toggleVisibility);\n    }, []);\n    const scrollToTop = ()=>{\n        window.scrollTo({\n            top: 0,\n            behavior: \"smooth\"\n        });\n    };\n    if (!isVisible) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        onClick: scrollToTop,\n        className: \"fixed bottom-8 right-8 z-50 p-3 bg-darkweb-accent text-darkweb-bg rounded-full shadow-lg hover:bg-darkweb-purple transition-all duration-300 hover:scale-110 glow-accent\",\n        \"aria-label\": \"Scroll to top\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            className: \"w-6 h-6\"\n        }, void 0, false, {\n            fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/ui/ScrollToTop.tsx\",\n            lineNumber: 40,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/components/ui/ScrollToTop.tsx\",\n        lineNumber: 35,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/ScrollToTop.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"e848f83e28e4\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQHBvcnRmb2xpby9jbGllbnQvLi9zcmMvYXBwL2dsb2JhbHMuY3NzPzgzMGIiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCJlODQ4ZjgzZTI4ZTRcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\",\"display\":\"swap\"}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-inter\\\",\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Fira_Code_arguments_subsets_latin_variable_font_fira_code_display_swap_variableName_firaCode___WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Fira_Code\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-fira-code\",\"display\":\"swap\"}],\"variableName\":\"firaCode\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Fira_Code\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-fira-code\\\",\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"firaCode\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Fira_Code_arguments_subsets_latin_variable_font_fira_code_display_swap_variableName_firaCode___WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Fira_Code_arguments_subsets_latin_variable_font_fira_code_display_swap_variableName_firaCode___WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Orbitron_arguments_subsets_latin_variable_font_orbitron_display_swap_variableName_orbitron___WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Orbitron\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-orbitron\",\"display\":\"swap\"}],\"variableName\":\"orbitron\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Orbitron\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-orbitron\\\",\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"orbitron\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Orbitron_arguments_subsets_latin_variable_font_orbitron_display_swap_variableName_orbitron___WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Orbitron_arguments_subsets_latin_variable_font_orbitron_display_swap_variableName_orbitron___WEBPACK_IMPORTED_MODULE_10__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_providers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/providers */ \"(rsc)/./src/components/providers/index.tsx\");\n/* harmony import */ var _components_effects_ThreeBackground__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/effects/ThreeBackground */ \"(rsc)/./src/components/effects/ThreeBackground.tsx\");\n/* harmony import */ var _components_effects_ScanlineOverlay__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/effects/ScanlineOverlay */ \"(rsc)/./src/components/effects/ScanlineOverlay.tsx\");\n/* harmony import */ var _components_effects_CRTFlicker__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/effects/CRTFlicker */ \"(rsc)/./src/components/effects/CRTFlicker.tsx\");\n/* harmony import */ var _components_Analytics__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/Analytics */ \"(rsc)/./src/components/Analytics.tsx\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react-hot-toast */ \"(rsc)/./node_modules/react-hot-toast/dist/index.mjs\");\n\n\n\n\n\n\n\n\n\n\n\nconst metadata = {\n    title: {\n        default: \"Sahil Ali - Full Stack Developer & Cybersecurity Expert\",\n        template: \"%s | Sahil Ali\"\n    },\n    description: \"Full Stack Developer, Competitive Coder, and Cybersecurity Expert. Ex-Microsoft MLSA specializing in AI/ML, Web Development, and Embedded Systems.\",\n    keywords: [\n        \"Sahil Ali\",\n        \"thestarsahil\",\n        \"Full Stack Developer\",\n        \"Cybersecurity\",\n        \"AI/ML\",\n        \"React\",\n        \"Node.js\",\n        \"Python\",\n        \"C++\",\n        \"Embedded Systems\",\n        \"Microsoft MLSA\",\n        \"Competitive Programming\"\n    ],\n    authors: [\n        {\n            name: \"Sahil Ali\",\n            url: \"https://thestarsahil.me\"\n        }\n    ],\n    creator: \"Sahil Ali\",\n    publisher: \"Sahil Ali\",\n    formatDetection: {\n        email: false,\n        address: false,\n        telephone: false\n    },\n    metadataBase: new URL(\"http://localhost:3000\" || 0),\n    alternates: {\n        canonical: \"/\"\n    },\n    openGraph: {\n        type: \"website\",\n        locale: \"en_US\",\n        url: \"/\",\n        title: \"Sahil Ali - Full Stack Developer & Cybersecurity Expert\",\n        description: \"Full Stack Developer, Competitive Coder, and Cybersecurity Expert. Ex-Microsoft MLSA specializing in AI/ML, Web Development, and Embedded Systems.\",\n        siteName: \"Sahil Ali Portfolio\",\n        images: [\n            {\n                url: \"/og-image.jpg\",\n                width: 1200,\n                height: 630,\n                alt: \"Sahil Ali - Portfolio\"\n            }\n        ]\n    },\n    twitter: {\n        card: \"summary_large_image\",\n        title: \"Sahil Ali - Full Stack Developer & Cybersecurity Expert\",\n        description: \"Full Stack Developer, Competitive Coder, and Cybersecurity Expert.\",\n        images: [\n            \"/og-image.jpg\"\n        ],\n        creator: \"@thestarsahil\"\n    },\n    robots: {\n        index: true,\n        follow: true,\n        googleBot: {\n            index: true,\n            follow: true,\n            \"max-video-preview\": -1,\n            \"max-image-preview\": \"large\",\n            \"max-snippet\": -1\n        }\n    },\n    verification: {\n        google: process.env.NEXT_PUBLIC_GOOGLE_VERIFICATION\n    },\n    category: \"technology\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: \"dark\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        type: \"image/svg+xml\",\n                        href: \"/favicon.svg\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/app/layout.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        type: \"image/png\",\n                        href: \"/favicon.png\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/app/layout.tsx\",\n                        lineNumber: 110,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"apple-touch-icon\",\n                        href: \"/apple-touch-icon.png\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/app/layout.tsx\",\n                        lineNumber: 111,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"manifest\",\n                        href: \"/manifest.json\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/app/layout.tsx\",\n                        lineNumber: 112,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"theme-color\",\n                        content: \"#00ffff\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/app/layout.tsx\",\n                        lineNumber: 113,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"color-scheme\",\n                        content: \"dark\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/app/layout.tsx\",\n                        lineNumber: 114,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1, viewport-fit=cover\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/app/layout.tsx\",\n                        lineNumber: 115,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/app/layout.tsx\",\n                lineNumber: 108,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_8___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Fira_Code_arguments_subsets_latin_variable_font_fira_code_display_swap_variableName_firaCode___WEBPACK_IMPORTED_MODULE_9___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Orbitron_arguments_subsets_latin_variable_font_orbitron_display_swap_variableName_orbitron___WEBPACK_IMPORTED_MODULE_10___default().variable)} antialiased`,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_providers__WEBPACK_IMPORTED_MODULE_2__.Providers, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_effects_ThreeBackground__WEBPACK_IMPORTED_MODULE_3__.ThreeBackground, {}, void 0, false, {\n                            fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/app/layout.tsx\",\n                            lineNumber: 120,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_effects_ScanlineOverlay__WEBPACK_IMPORTED_MODULE_4__.ScanlineOverlay, {}, void 0, false, {\n                            fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/app/layout.tsx\",\n                            lineNumber: 121,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_effects_CRTFlicker__WEBPACK_IMPORTED_MODULE_5__.CRTFlicker, {}, void 0, false, {\n                            fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/app/layout.tsx\",\n                            lineNumber: 122,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                            className: \"relative z-10\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/app/layout.tsx\",\n                            lineNumber: 125,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_7__.Toaster, {\n                            position: \"bottom-right\",\n                            toastOptions: {\n                                duration: 4000,\n                                style: {\n                                    background: \"#1a1a1a\",\n                                    color: \"#00ffff\",\n                                    border: \"1px solid #333333\",\n                                    fontFamily: \"var(--font-fira-code)\",\n                                    fontSize: \"14px\"\n                                },\n                                success: {\n                                    iconTheme: {\n                                        primary: \"#00ff00\",\n                                        secondary: \"#1a1a1a\"\n                                    }\n                                },\n                                error: {\n                                    iconTheme: {\n                                        primary: \"#ff0000\",\n                                        secondary: \"#1a1a1a\"\n                                    }\n                                }\n                            }\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/app/layout.tsx\",\n                            lineNumber: 130,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Analytics__WEBPACK_IMPORTED_MODULE_6__.Analytics, {}, void 0, false, {\n                            fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/app/layout.tsx\",\n                            lineNumber: 157,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/app/layout.tsx\",\n                    lineNumber: 118,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/app/layout.tsx\",\n                lineNumber: 117,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/app/layout.tsx\",\n        lineNumber: 107,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_layout_Navbar__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/layout/Navbar */ \"(rsc)/./src/components/layout/Navbar.tsx\");\n/* harmony import */ var _components_sections_Hero__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/sections/Hero */ \"(rsc)/./src/components/sections/Hero.tsx\");\n/* harmony import */ var _components_sections_About__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/sections/About */ \"(rsc)/./src/components/sections/About.tsx\");\n/* harmony import */ var _components_sections_Services__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/sections/Services */ \"(rsc)/./src/components/sections/Services.tsx\");\n/* harmony import */ var _components_sections_Skills__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/sections/Skills */ \"(rsc)/./src/components/sections/Skills.tsx\");\n/* harmony import */ var _components_sections_Projects__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/sections/Projects */ \"(rsc)/./src/components/sections/Projects.tsx\");\n/* harmony import */ var _components_sections_Contact__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/sections/Contact */ \"(rsc)/./src/components/sections/Contact.tsx\");\n/* harmony import */ var _components_layout_Footer__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/layout/Footer */ \"(rsc)/./src/components/layout/Footer.tsx\");\n/* harmony import */ var _components_ui_ScrollToTop__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/ScrollToTop */ \"(rsc)/./src/components/ui/ScrollToTop.tsx\");\n\n\n\n\n\n\n\n\n\n\nfunction HomePage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Navbar__WEBPACK_IMPORTED_MODULE_1__.Navbar, {}, void 0, false, {\n                fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/app/page.tsx\",\n                lineNumber: 14,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sections_Hero__WEBPACK_IMPORTED_MODULE_2__.Hero, {}, void 0, false, {\n                fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/app/page.tsx\",\n                lineNumber: 15,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sections_About__WEBPACK_IMPORTED_MODULE_3__.About, {}, void 0, false, {\n                fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/app/page.tsx\",\n                lineNumber: 16,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sections_Services__WEBPACK_IMPORTED_MODULE_4__.Services, {}, void 0, false, {\n                fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/app/page.tsx\",\n                lineNumber: 17,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sections_Skills__WEBPACK_IMPORTED_MODULE_5__.Skills, {}, void 0, false, {\n                fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/app/page.tsx\",\n                lineNumber: 18,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sections_Projects__WEBPACK_IMPORTED_MODULE_6__.Projects, {}, void 0, false, {\n                fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/app/page.tsx\",\n                lineNumber: 19,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sections_Contact__WEBPACK_IMPORTED_MODULE_7__.Contact, {}, void 0, false, {\n                fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/app/page.tsx\",\n                lineNumber: 20,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Footer__WEBPACK_IMPORTED_MODULE_8__.Footer, {}, void 0, false, {\n                fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/app/page.tsx\",\n                lineNumber: 21,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ScrollToTop__WEBPACK_IMPORTED_MODULE_9__.ScrollToTop, {}, void 0, false, {\n                fileName: \"/home/<USER>/Documents/GitHub/Portfolio/client/src/app/page.tsx\",\n                lineNumber: 22,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL3BhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBb0Q7QUFDRjtBQUNFO0FBQ007QUFDSjtBQUNJO0FBQ0Y7QUFDSjtBQUNNO0FBRTNDLFNBQVNTO0lBQ3RCLHFCQUNFOzswQkFDRSw4REFBQ1QsNkRBQU1BOzs7OzswQkFDUCw4REFBQ0MsMkRBQUlBOzs7OzswQkFDTCw4REFBQ0MsNkRBQUtBOzs7OzswQkFDTiw4REFBQ0MsbUVBQVFBOzs7OzswQkFDVCw4REFBQ0MsK0RBQU1BOzs7OzswQkFDUCw4REFBQ0MsbUVBQVFBOzs7OzswQkFDVCw4REFBQ0MsaUVBQU9BOzs7OzswQkFDUiw4REFBQ0MsNkRBQU1BOzs7OzswQkFDUCw4REFBQ0MsbUVBQVdBOzs7Ozs7O0FBR2xCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQHBvcnRmb2xpby9jbGllbnQvLi9zcmMvYXBwL3BhZ2UudHN4P2Y2OGEiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgTmF2YmFyIH0gZnJvbSAnQC9jb21wb25lbnRzL2xheW91dC9OYXZiYXInO1xuaW1wb3J0IHsgSGVybyB9IGZyb20gJ0AvY29tcG9uZW50cy9zZWN0aW9ucy9IZXJvJztcbmltcG9ydCB7IEFib3V0IH0gZnJvbSAnQC9jb21wb25lbnRzL3NlY3Rpb25zL0Fib3V0JztcbmltcG9ydCB7IFNlcnZpY2VzIH0gZnJvbSAnQC9jb21wb25lbnRzL3NlY3Rpb25zL1NlcnZpY2VzJztcbmltcG9ydCB7IFNraWxscyB9IGZyb20gJ0AvY29tcG9uZW50cy9zZWN0aW9ucy9Ta2lsbHMnO1xuaW1wb3J0IHsgUHJvamVjdHMgfSBmcm9tICdAL2NvbXBvbmVudHMvc2VjdGlvbnMvUHJvamVjdHMnO1xuaW1wb3J0IHsgQ29udGFjdCB9IGZyb20gJ0AvY29tcG9uZW50cy9zZWN0aW9ucy9Db250YWN0JztcbmltcG9ydCB7IEZvb3RlciB9IGZyb20gJ0AvY29tcG9uZW50cy9sYXlvdXQvRm9vdGVyJztcbmltcG9ydCB7IFNjcm9sbFRvVG9wIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL1Njcm9sbFRvVG9wJztcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gSG9tZVBhZ2UoKSB7XG4gIHJldHVybiAoXG4gICAgPD5cbiAgICAgIDxOYXZiYXIgLz5cbiAgICAgIDxIZXJvIC8+XG4gICAgICA8QWJvdXQgLz5cbiAgICAgIDxTZXJ2aWNlcyAvPlxuICAgICAgPFNraWxscyAvPlxuICAgICAgPFByb2plY3RzIC8+XG4gICAgICA8Q29udGFjdCAvPlxuICAgICAgPEZvb3RlciAvPlxuICAgICAgPFNjcm9sbFRvVG9wIC8+XG4gICAgPC8+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiTmF2YmFyIiwiSGVybyIsIkFib3V0IiwiU2VydmljZXMiLCJTa2lsbHMiLCJQcm9qZWN0cyIsIkNvbnRhY3QiLCJGb290ZXIiLCJTY3JvbGxUb1RvcCIsIkhvbWVQYWdlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/page.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/Analytics.tsx":
/*!**************************************!*\
  !*** ./src/components/Analytics.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Analytics: () => (/* binding */ e0),
/* harmony export */   trackContactForm: () => (/* binding */ e3),
/* harmony export */   trackEvent: () => (/* binding */ e1),
/* harmony export */   trackPageView: () => (/* binding */ e2),
/* harmony export */   trackProjectView: () => (/* binding */ e4),
/* harmony export */   trackResumeDownload: () => (/* binding */ e5),
/* harmony export */   trackSocialClick: () => (/* binding */ e6)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/home/<USER>/Documents/GitHub/Portfolio/client/src/components/Analytics.tsx#Analytics`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/home/<USER>/Documents/GitHub/Portfolio/client/src/components/Analytics.tsx#trackEvent`);

const e2 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/home/<USER>/Documents/GitHub/Portfolio/client/src/components/Analytics.tsx#trackPageView`);

const e3 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/home/<USER>/Documents/GitHub/Portfolio/client/src/components/Analytics.tsx#trackContactForm`);

const e4 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/home/<USER>/Documents/GitHub/Portfolio/client/src/components/Analytics.tsx#trackProjectView`);

const e5 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/home/<USER>/Documents/GitHub/Portfolio/client/src/components/Analytics.tsx#trackResumeDownload`);

const e6 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/home/<USER>/Documents/GitHub/Portfolio/client/src/components/Analytics.tsx#trackSocialClick`);


/***/ }),

/***/ "(rsc)/./src/components/effects/CRTFlicker.tsx":
/*!***********************************************!*\
  !*** ./src/components/effects/CRTFlicker.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   CRTFlicker: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/home/<USER>/Documents/GitHub/Portfolio/client/src/components/effects/CRTFlicker.tsx#CRTFlicker`);


/***/ }),

/***/ "(rsc)/./src/components/effects/ScanlineOverlay.tsx":
/*!****************************************************!*\
  !*** ./src/components/effects/ScanlineOverlay.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ScanlineOverlay: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/home/<USER>/Documents/GitHub/Portfolio/client/src/components/effects/ScanlineOverlay.tsx#ScanlineOverlay`);


/***/ }),

/***/ "(rsc)/./src/components/effects/ThreeBackground.tsx":
/*!****************************************************!*\
  !*** ./src/components/effects/ThreeBackground.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ThreeBackground: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/home/<USER>/Documents/GitHub/Portfolio/client/src/components/effects/ThreeBackground.tsx#ThreeBackground`);


/***/ }),

/***/ "(rsc)/./src/components/layout/Footer.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Footer.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Footer: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/home/<USER>/Documents/GitHub/Portfolio/client/src/components/layout/Footer.tsx#Footer`);


/***/ }),

/***/ "(rsc)/./src/components/layout/Navbar.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Navbar.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Navbar: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/home/<USER>/Documents/GitHub/Portfolio/client/src/components/layout/Navbar.tsx#Navbar`);


/***/ }),

/***/ "(rsc)/./src/components/providers/index.tsx":
/*!********************************************!*\
  !*** ./src/components/providers/index.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Providers: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/home/<USER>/Documents/GitHub/Portfolio/client/src/components/providers/index.tsx#Providers`);


/***/ }),

/***/ "(rsc)/./src/components/sections/About.tsx":
/*!*******************************************!*\
  !*** ./src/components/sections/About.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   About: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/About.tsx#About`);


/***/ }),

/***/ "(rsc)/./src/components/sections/Contact.tsx":
/*!*********************************************!*\
  !*** ./src/components/sections/Contact.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Contact: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Contact.tsx#Contact`);


/***/ }),

/***/ "(rsc)/./src/components/sections/Hero.tsx":
/*!******************************************!*\
  !*** ./src/components/sections/Hero.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Hero: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Hero.tsx#Hero`);


/***/ }),

/***/ "(rsc)/./src/components/sections/Projects.tsx":
/*!**********************************************!*\
  !*** ./src/components/sections/Projects.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Projects: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Projects.tsx#Projects`);


/***/ }),

/***/ "(rsc)/./src/components/sections/Services.tsx":
/*!**********************************************!*\
  !*** ./src/components/sections/Services.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Services: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Services.tsx#Services`);


/***/ }),

/***/ "(rsc)/./src/components/sections/Skills.tsx":
/*!********************************************!*\
  !*** ./src/components/sections/Skills.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Skills: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/home/<USER>/Documents/GitHub/Portfolio/client/src/components/sections/Skills.tsx#Skills`);


/***/ }),

/***/ "(rsc)/./src/components/ui/ScrollToTop.tsx":
/*!*******************************************!*\
  !*** ./src/components/ui/ScrollToTop.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ScrollToTop: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/home/<USER>/Documents/GitHub/Portfolio/client/src/components/ui/ScrollToTop.tsx#ScrollToTop`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/three","vendor-chunks/react-query","vendor-chunks/react-hot-toast","vendor-chunks/match-sorter","vendor-chunks/remove-accents","vendor-chunks/goober","vendor-chunks/@babel","vendor-chunks/framer-motion","vendor-chunks/lucide-react","vendor-chunks/zod","vendor-chunks/@hookform","vendor-chunks/react-hook-form"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fthestarsahil%2FDocuments%2FGitHub%2FPortfolio%2Fclient&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();