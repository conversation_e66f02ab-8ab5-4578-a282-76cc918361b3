import { Router } from 'express';
import { ContactMessage } from '@/models/ContactMessage';
import { Analytics } from '@/models/Analytics';
import { asyncHandler } from '@/middleware/errorHandler';
import { authenticate, authorize } from '@/middleware/auth';
import { HTTP_STATUS, UserRole } from '@portfolio/shared';

const router = Router();

// Admin routes (admin only)
router.use(authenticate);
router.use(authorize(UserRole.ADMIN));

// Dashboard stats
router.get('/dashboard', asyncHandler(async (req, res) => {
  const [
    totalMessages,
    unreadMessages,
    totalEvents,
    todayEvents,
  ] = await Promise.all([
    ContactMessage.countDocuments(),
    ContactMessage.countDocuments({ isRead: false }),
    Analytics.countDocuments(),
    Analytics.countDocuments({
      timestamp: {
        $gte: new Date(new Date().setHours(0, 0, 0, 0)),
      },
    }),
  ]);

  res.status(HTTP_STATUS.OK).json({
    success: true,
    data: {
      totalMessages,
      unreadMessages,
      totalEvents,
      todayEvents,
    },
    timestamp: new Date().toISOString(),
  });
}));

// System stats
router.get('/stats', asyncHandler(async (req, res) => {
  const [
    recentMessages,
    recentEvents,
  ] = await Promise.all([
    ContactMessage.find().sort({ createdAt: -1 }).limit(5),
    Analytics.find().sort({ timestamp: -1 }).limit(10),
  ]);

  res.status(HTTP_STATUS.OK).json({
    success: true,
    data: {
      recentMessages,
      recentEvents,
    },
    timestamp: new Date().toISOString(),
  });
}));

export default router;
