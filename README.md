# Sahil Ali - Portfolio

A modern full-stack portfolio application with client-server architecture.

## 🏗️ Architecture

```
Portfolio/
├── client/                 # Next.js frontend application
│   ├── src/
│   │   ├── components/     # React components
│   │   ├── pages/         # Next.js pages
│   │   ├── styles/        # CSS/SCSS styles
│   │   ├── hooks/         # Custom React hooks
│   │   ├── utils/         # Utility functions
│   │   └── types/         # TypeScript type definitions
│   ├── public/            # Static assets
│   ├── package.json
│   └── next.config.js
├── server/                # Node.js/Express backend API
│   ├── src/
│   │   ├── controllers/   # API controllers
│   │   ├── models/        # Database models
│   │   ├── routes/        # API routes
│   │   ├── middleware/    # Express middleware
│   │   ├── services/      # Business logic
│   │   └── utils/         # Server utilities
│   ├── package.json
│   └── server.js
├── shared/                # Shared types and utilities
├── docker-compose.yml     # Docker configuration
└── README.md
```

## 🚀 Tech Stack

### Frontend (Client)
- **Next.js 14** - React framework with SSR/SSG
- **TypeScript** - Type safety
- **Tailwind CSS** - Utility-first CSS framework
- **Framer Motion** - Animations
- **Three.js** - 3D graphics and effects
- **React Hook Form** - Form handling

### Backend (Server)
- **Node.js** - Runtime environment
- **Express.js** - Web framework
- **TypeScript** - Type safety
- **MongoDB** - Database
- **Mongoose** - ODM for MongoDB
- **Nodemailer** - Email service
- **JWT** - Authentication
- **Helmet** - Security middleware

### DevOps
- **Docker** - Containerization
- **Docker Compose** - Multi-container orchestration
- **GitHub Actions** - CI/CD pipeline
- **Vercel** - Frontend deployment
- **Railway/Heroku** - Backend deployment

## 🎨 Features

### Current Features (Migrated)
- ✅ Dark web/cybersecurity theme
- ✅ Terminal-style interface
- ✅ Glitch effects and animations
- ✅ Three.js particle background
- ✅ Project showcase carousel
- ✅ Skills visualization
- ✅ Responsive design

### New Features (Enhanced)
- 🆕 Contact form with email notifications
- 🆕 Project management API
- 🆕 Visitor analytics
- 🆕 Blog/articles section
- 🆕 Admin dashboard
- 🆕 Real-time chat widget
- 🆕 Performance monitoring

## 🛠️ Development

### Prerequisites
- Node.js 18+
- npm/yarn/pnpm
- Docker (optional)
- MongoDB (local or cloud)
- Redis (optional, for caching)

### Quick Start

1. **Clone the repository**
   ```bash
   git clone https://github.com/thestarsahil/Portfolio.git
   cd Portfolio
   ```

2. **Install dependencies**
   ```bash
   # Install all dependencies (root, client, server, shared)
   npm run setup

   # Or install individually
   npm install
   cd client && npm install
   cd ../server && npm install
   cd ../shared && npm install
   ```

3. **Environment setup**
   ```bash
   # Copy environment files
   cp .env.example .env
   cp client/.env.example client/.env.local
   cp server/.env.example server/.env

   # Edit the environment files with your actual values
   ```

4. **Database setup**
   ```bash
   # Option 1: Use Docker (recommended)
   docker-compose up -d mongodb redis

   # Option 2: Install MongoDB and Redis locally
   # Follow installation guides for your OS
   ```

5. **Seed the database**
   ```bash
   cd server && npm run seed
   ```

6. **Start development servers**
   ```bash
   # Option 1: Start all services with Docker
   docker-compose up

   # Option 2: Start manually
   # Terminal 1 - Start both client and server
   npm run dev

   # Or start individually:
   # Terminal 1 - Start client (Next.js)
   cd client && npm run dev

   # Terminal 2 - Start server (Express)
   cd server && npm run dev
   ```

7. **Access the application**
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:5000
   - Health Check: http://localhost:5000/health

### Docker Development

```bash
# Start all services with Docker Compose
docker-compose up -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down
```

## 📦 Deployment

### Frontend (Vercel)
```bash
cd client
vercel --prod
```

### Backend (Railway)
```bash
cd server
railway login
railway deploy
```

## 🔧 Configuration

### Environment Variables

#### Client (.env.local)
```env
NEXT_PUBLIC_API_URL=http://localhost:5000
NEXT_PUBLIC_SITE_URL=http://localhost:3000
NEXT_PUBLIC_GOOGLE_ANALYTICS_ID=your_ga_id
```

#### Server (.env)
```env
NODE_ENV=development
PORT=5000
MONGODB_URI=mongodb://localhost:27017/portfolio
JWT_SECRET=your_jwt_secret
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your_app_password
```

## 📝 API Documentation

### Endpoints

#### Contact
- `POST /api/contact` - Send contact message
- `GET /api/contact` - Get contact messages (admin)

#### Projects
- `GET /api/projects` - Get all projects
- `POST /api/projects` - Create project (admin)
- `PUT /api/projects/:id` - Update project (admin)
- `DELETE /api/projects/:id` - Delete project (admin)

#### Analytics
- `POST /api/analytics/visit` - Track page visit
- `GET /api/analytics/stats` - Get analytics data (admin)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 👨‍💻 Author

**Sahil Ali**
- Website: [thestarsahil.me](https://thestarsahil.me)
- GitHub: [@thestarsahil](https://github.com/thestarsahil)
- LinkedIn: [thestarsahil](https://linkedin.com/in/thestarsahil)
- Email: <EMAIL>
