import { Request, Response } from 'express';
import { validationResult } from 'express-validator';
import { ContactMessage } from '@/models/ContactMessage';
import { Analytics } from '@/models/Analytics';
import { emailService } from '@/services/emailService';
import { asyncHand<PERSON>, CustomError } from '@/middleware/errorHandler';
import { AuthRequest } from '@/middleware/auth';
import { HTTP_STATUS, SUCCESS_MESSAGES, AnalyticsEvent } from '@portfolio/shared';

export const createContactMessage = asyncHandler(async (req: Request, res: Response) => {
  // Check for validation errors
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new CustomError('Validation failed', HTTP_STATUS.BAD_REQUEST);
  }

  const { name, email, subject, message } = req.body;

  // Get client information
  const ipAddress = req.ip;
  const userAgent = req.get('User-Agent');

  // Create contact message
  const contactMessage = await ContactMessage.create({
    name,
    email,
    subject,
    message,
    ipAddress,
    userAgent,
  });

  // Track analytics
  await Analytics.create({
    event: AnalyticsEvent.CONTACT_FORM_SUBMIT,
    page: '/contact',
    ipAddress,
    userAgent,
    metadata: {
      contactMessageId: contactMessage._id,
      subject,
    },
  });

  // Send notification email to admin
  try {
    await emailService.sendContactNotification({
      name,
      email,
      subject,
      message,
      contactMessageId: contactMessage._id.toString(),
    });
  } catch (emailError) {
    // Log error but don't fail the request
    console.error('Failed to send contact notification email:', emailError);
  }

  // Send confirmation email to user
  try {
    await emailService.sendContactConfirmation({
      name,
      email,
      subject,
    });
  } catch (emailError) {
    // Log error but don't fail the request
    console.error('Failed to send contact confirmation email:', emailError);
  }

  res.status(HTTP_STATUS.CREATED).json({
    success: true,
    message: SUCCESS_MESSAGES.CONTACT_SENT,
    data: {
      id: contactMessage._id,
      name: contactMessage.name,
      email: contactMessage.email,
      subject: contactMessage.subject,
      createdAt: contactMessage.createdAt,
    },
    timestamp: new Date().toISOString(),
  });
});

export const getContactMessages = asyncHandler(async (req: AuthRequest, res: Response) => {
  const page = parseInt(req.query.page as string) || 1;
  const limit = parseInt(req.query.limit as string) || 10;
  const skip = (page - 1) * limit;

  const filter: any = {};
  
  // Filter by read status
  if (req.query.isRead !== undefined) {
    filter.isRead = req.query.isRead === 'true';
  }
  
  // Filter by replied status
  if (req.query.isReplied !== undefined) {
    filter.isReplied = req.query.isReplied === 'true';
  }

  // Search by name, email, or subject
  if (req.query.search) {
    const searchRegex = new RegExp(req.query.search as string, 'i');
    filter.$or = [
      { name: searchRegex },
      { email: searchRegex },
      { subject: searchRegex },
    ];
  }

  const [messages, total] = await Promise.all([
    ContactMessage.find(filter)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit),
    ContactMessage.countDocuments(filter),
  ]);

  const totalPages = Math.ceil(total / limit);

  res.status(HTTP_STATUS.OK).json({
    success: true,
    data: messages,
    pagination: {
      page,
      limit,
      total,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1,
    },
    timestamp: new Date().toISOString(),
  });
});

export const getContactMessage = asyncHandler(async (req: AuthRequest, res: Response) => {
  const { id } = req.params;

  const message = await ContactMessage.findById(id);
  if (!message) {
    throw new CustomError('Contact message not found', HTTP_STATUS.NOT_FOUND);
  }

  res.status(HTTP_STATUS.OK).json({
    success: true,
    data: message,
    timestamp: new Date().toISOString(),
  });
});

export const markAsRead = asyncHandler(async (req: AuthRequest, res: Response) => {
  const { id } = req.params;

  const message = await ContactMessage.findByIdAndUpdate(
    id,
    { isRead: true },
    { new: true }
  );

  if (!message) {
    throw new CustomError('Contact message not found', HTTP_STATUS.NOT_FOUND);
  }

  res.status(HTTP_STATUS.OK).json({
    success: true,
    message: 'Message marked as read',
    data: message,
    timestamp: new Date().toISOString(),
  });
});

export const markAsReplied = asyncHandler(async (req: AuthRequest, res: Response) => {
  const { id } = req.params;

  const message = await ContactMessage.findByIdAndUpdate(
    id,
    { isReplied: true },
    { new: true }
  );

  if (!message) {
    throw new CustomError('Contact message not found', HTTP_STATUS.NOT_FOUND);
  }

  res.status(HTTP_STATUS.OK).json({
    success: true,
    message: 'Message marked as replied',
    data: message,
    timestamp: new Date().toISOString(),
  });
});

export const deleteContactMessage = asyncHandler(async (req: AuthRequest, res: Response) => {
  const { id } = req.params;

  const message = await ContactMessage.findByIdAndDelete(id);
  if (!message) {
    throw new CustomError('Contact message not found', HTTP_STATUS.NOT_FOUND);
  }

  res.status(HTTP_STATUS.OK).json({
    success: true,
    message: 'Contact message deleted successfully',
    timestamp: new Date().toISOString(),
  });
});

export const getContactStats = asyncHandler(async (req: AuthRequest, res: Response) => {
  const [total, unread, unreplied, thisMonth] = await Promise.all([
    ContactMessage.countDocuments(),
    ContactMessage.countDocuments({ isRead: false }),
    ContactMessage.countDocuments({ isReplied: false }),
    ContactMessage.countDocuments({
      createdAt: {
        $gte: new Date(new Date().getFullYear(), new Date().getMonth(), 1),
      },
    }),
  ]);

  res.status(HTTP_STATUS.OK).json({
    success: true,
    data: {
      total,
      unread,
      unreplied,
      thisMonth,
    },
    timestamp: new Date().toISOString(),
  });
});
