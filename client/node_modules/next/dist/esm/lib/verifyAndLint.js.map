{"version": 3, "sources": ["../../src/lib/verifyAndLint.ts"], "names": ["red", "Worker", "existsSync", "join", "ESLINT_DEFAULT_DIRS", "eventLintCheckCompleted", "CompileError", "isError", "verifyAndLint", "dir", "cacheLocation", "configLintDirs", "enableWorkerThreads", "telemetry", "lintWorkers", "require", "resolve", "exposedMethods", "numWorkers", "maxRetries", "lintDirs", "reduce", "res", "d", "currDir", "push", "lintResults", "runLintCheck", "lintDuringBuild", "eslintOptions", "lintOutput", "output", "eventInfo", "record", "buildLint", "flush", "console", "log", "err", "type", "error", "message", "process", "exit", "end"], "mappings": "AAAA,SAASA,GAAG,QAAQ,eAAc;AAClC,SAASC,MAAM,QAAQ,WAAU;AACjC,SAASC,UAAU,QAAQ,KAAI;AAC/B,SAASC,IAAI,QAAQ,OAAM;AAC3B,SAASC,mBAAmB,QAAQ,cAAa;AAEjD,SAASC,uBAAuB,QAAQ,sBAAqB;AAC7D,SAASC,YAAY,QAAQ,kBAAiB;AAC9C,OAAOC,aAAa,aAAY;AAEhC,OAAO,eAAeC,cACpBC,GAAW,EACXC,aAAqB,EACrBC,cAAoC,EACpCC,mBAAwC,EACxCC,SAAoB;IAEpB,IAAIC;IAMJ,IAAI;QACFA,cAAc,IAAIb,OAAOc,QAAQC,OAAO,CAAC,0BAA0B;YACjEC,gBAAgB;gBAAC;aAAe;YAChCC,YAAY;YACZN;YACAO,YAAY;QACd;QAIA,MAAMC,WAAW,AAACT,CAAAA,kBAAkBP,mBAAkB,EAAGiB,MAAM,CAC7D,CAACC,KAAeC;YACd,MAAMC,UAAUrB,KAAKM,KAAKc;YAC1B,IAAI,CAACrB,WAAWsB,UAAU,OAAOF;YACjCA,IAAIG,IAAI,CAACD;YACT,OAAOF;QACT,GACA,EAAE;QAGJ,MAAMI,cAAc,OAAMZ,+BAAAA,YAAaa,YAAY,CAAClB,KAAKW,UAAU;YACjEQ,iBAAiB;YACjBC,eAAe;gBACbnB;YACF;QACF;QACA,MAAMoB,aACJ,OAAOJ,gBAAgB,WAAWA,cAAcA,+BAAAA,YAAaK,MAAM;QAErE,IAAI,OAAOL,gBAAgB,aAAYA,+BAAAA,YAAaM,SAAS,GAAE;YAC7DnB,UAAUoB,MAAM,CACd5B,wBAAwB;gBACtB,GAAGqB,YAAYM,SAAS;gBACxBE,WAAW;YACb;QAEJ;QAEA,IAAI,OAAOR,gBAAgB,aAAYA,+BAAAA,YAAanB,OAAO,KAAIuB,YAAY;YACzE,MAAMjB,UAAUsB,KAAK;YACrB,MAAM,IAAI7B,aAAawB;QACzB;QAEA,IAAIA,YAAY;YACdM,QAAQC,GAAG,CAACP;QACd;IACF,EAAE,OAAOQ,KAAK;QACZ,IAAI/B,QAAQ+B,MAAM;YAChB,IAAIA,IAAIC,IAAI,KAAK,kBAAkBD,eAAehC,cAAc;gBAC9D8B,QAAQI,KAAK,CAACxC,IAAI;gBAClBoC,QAAQI,KAAK,CAACF,IAAIG,OAAO;gBACzBC,QAAQC,IAAI,CAAC;YACf,OAAO,IAAIL,IAAIC,IAAI,KAAK,cAAc;gBACpCH,QAAQI,KAAK,CAACF,IAAIG,OAAO;gBACzBC,QAAQC,IAAI,CAAC;YACf;QACF;QACA,MAAML;IACR,SAAU;QACR,IAAI;YACFxB,+BAAAA,YAAa8B,GAAG;QAClB,EAAE,OAAM,CAAC;IACX;AACF"}